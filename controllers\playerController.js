const {
  PlayerDetail,
  User,
  Tournament,
  Otp,
  Registration,
  Notifications,
  ClubDetail,
  InviteRequest,
  Certificate,
} = require("../config/db").models;

const { v4: uuidv4 } = require("uuid");
const { Op } = require("sequelize");
const {
  playerDetailSchema,
  getAllPlayerSchema,
  updatePlayerSchema,
} = require("../schema/playerSchama");
const { sendResponse, handleError } = require("../utils/apiResponse");

const { deleteFromS3, generatePresignedUrl } = require("../utils/s3");

const { exportToExcel, exportToPDF } = require("../utils/report-generation");
const emailService = require("../utils/mailer/emailService");
const { config } = require("../config/config");
const smsService = require("../utils/sms/smsService");
const { sequelize } = require("../config/db");
const { createRefund } = require("../utils/razorPay");

/*
 * Create a player profile * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */
const createPlayerProfile = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { data, success, error } = playerDetailSchema.safeParse(req.body);
    if (!success) {
      return sendResponse(res, 422, {
        success: false,
        error: error.errors,
      });
    }
    const playerData = data;
    const cbid = await generateCbid(playerData.countryCode || "IN");

    const club =
      playerData.other_club === "true" ? { club: playerData.club } : {};

    await User.update({ cbid }, { where: { id: userId } });
    const { profileUrl, myfile: newDocument } = req?.files;
    const document = newDocument ? newDocument[0] : null;

    const newFile = document && {
      id: uuidv4(),
      name: document?.originalname,
      url: document?.location,
      fileType: document?.mimetype,
      size: document?.size,
      createdAt: new Date().toISOString(),
    };

    const newPlayer = await PlayerDetail.create(
      {
        userId,
        profileUrl: profileUrl ? profileUrl?.location : null,
        myFiles: newFile ? [newFile] : [],
        ...playerData,
        ...club,
      },
      {
        include: [User],
      }
    );
    if (playerData.other_clubs === "false") {
      const clubInviteResponse = await sendClubInviteHandler({
        userId,
        clubName: playerData?.clubs?.clubName,
        clubId: playerData?.clubs?.id,
      });
    }
    sendResponse(res, 201, {
      success: true,
      data: newPlayer,
    });
  } catch (error) {
    console.error("Error creating player:", error);
    deleteFromS3(req?.files?.profileImage?.location);
    deleteFromS3(req?.files?.myfile[0]?.location);
    handleError(res, error);
  }
};
/**
 * Edit a player profile * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object */
const editPlayerProfile = async (req, res) => {
  const userId = req.user.userId;

  try {
    const result = updatePlayerSchema.partial().safeParse(req.body);
    const { otp } = req.body;

    if (!result.success) {
      return sendResponse(res, 422, {
        success: false,
        error: result.error.errors,
      });
    }

    const playerData = result.data;
    const { name, phoneNumber, phoneChanged } = playerData;
    const update = {};

    const club =
      playerData.other_clubs === "true" ? { club: playerData.club } : {};

    if (playerData.other_clubs === "false") {
      const clubInviteResponse = await sendClubInviteHandler({
        userId,
        clubName: playerData?.clubs?.clubName,
        clubId: playerData?.clubs?.id,
      });
    }
    if (phoneChanged) {
      if (!otp) {
        return sendResponse(res, 422, {
          success: false,
          error: { message: "otp not found" },
        });
      }
      const existingOtp = await Otp.findOne({
        where: { phoneNumber, type: "verification", platform: "sms" },
      });

      if (!existingOtp) {
        return sendResponse(res, 404, {
          success: false,
          error: { message: "OTP not found. Please request a new one." },
        });
      }
      const time = new Date(existingOtp.expiresAt);
      if (time < new Date()) {
        return sendResponse(res, 400, {
          success: false,
          error: { message: "OTP has expired. Please request a new one." },
        });
      }

      if (existingOtp.otp !== otp) {
        return sendResponse(res, 401, {
          success: false,
          error: { message: "Incorrect OTP." },
        });
      }
      await existingOtp.destroy();
      update.phoneNumber = phoneNumber;
    }
    if (name) {
      update.name = name;
    }

    if (name || phoneChanged) {
      await User.update(update, { where: { id: userId } });
    }

    const existingPlayer = await PlayerDetail.findOne({
      where: { userId: userId },
    });

    if (!existingPlayer) {
      return sendResponse(res, 404, {
        success: false,
        error: "Player not found",
      });
    }
    const profileUrl = req?.file
      ? req?.file?.location
      : existingPlayer?.profileUrl || null;
    const updatedPlayer = await PlayerDetail.update(
      { ...playerData, profileUrl, ...club },
      {
        where: { userId: userId },
        returning: true,
      }
    );

    if (existingPlayer?.profileUrl) {
      deleteFromS3(existingPlayer?.profileUrl);
    }

    if (updatedPlayer[0] === 0) {
      return sendResponse(res, 404, {
        success: false,
        error: "Player not found",
      });
    }
    sendResponse(res, 200, {
      success: true,
      data: updatedPlayer[1][0],
    });
  } catch (error) {
    deleteFromS3(req?.file?.location);
    handleError(res, error);
  }
};

const getAllPlayer = async (req, res) => {
  const { data, success, error } = getAllPlayerSchema.safeParse(req.query);

  if (!success) {
    return sendResponse(res, 422, {
      success: false,
      error: "Invalid query parameters",
    });
  }
  try {
    const {
      page = 1,
      limit = 10,
      city,
      playerId,
      playerName,
      country,
      state,
      district,
    } = data;
    const offset = (page - 1) * limit;
    const whereClause = {};
    const filterFields = { city, country, state, district };
    Object.entries(filterFields).forEach(([key, value]) => {
      if (value) whereClause[key] = { [Op.iLike]: `%${value}%` };
    });
    if (playerId && !playerId.toLowerCase().startsWith("cb")) {
      whereClause[Op.or] = [
        { fideId: { [Op.iLike]: `%${playerId}%` } },
        { aicfId: { [Op.iLike]: `%${playerId}%` } },
        { stateId: { [Op.iLike]: `%${playerId}%` } },
        { districtId: { [Op.iLike]: `%${playerId}%` } },
      ];
    }
    let userWhereClause = {};
    if (playerId && playerId.toLowerCase().startsWith("cb")) {
      userWhereClause = { cbid: { [Op.iLike]: `%${playerId}%` } };
    }
    if (playerName) userWhereClause.name = { [Op.iLike]: `%${playerName}%` };
    const { rows: players, count } = await PlayerDetail.findAndCountAll({
      where: whereClause,
      isActive: { [Op.ne]: false },
      offset,
      limit,
      attributes: [
        "playerTitle",
        "fideRating",
        "fideId",
        "aicfId",
        "districtId",
        "stateId",
        "country",
        "state",
        "district",
        "city",
      ],
      include: [
        {
          model: User,
          where: userWhereClause,
          attributes: ["cbid", "name"],
          required: true,
        },
      ],
      order: [["fideRating", "DESC"]],
    });
    const formattedPlayers = players.map((player) => {
      const playerData = player.toJSON();
      if (player.User) {
        return { ...playerData, ...player.User.toJSON() };
      }
      return playerData;
    });
    const response = {
      players: formattedPlayers,
      total: count,
      currentPage: page,
      totalPages: Math.ceil(count / limit),
    };
    sendResponse(res, 200, {
      success: true,
      data: response,
    });
  } catch (error) {
    handleError(res, error);
  }
};
const getSinglePlayer = async (req, res) => {
  try {
    const { id } = req.params;
    if (!id || typeof id !== "string") {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid player ID",
      });
    }
    const player = await User.findOne({
      where: { cbid: id },
      attributes: ["name", "cbid", "id"],
      include: [
        {
          model: PlayerDetail,
          attributes: [
            "playerTitle",
            "clubId",
            "fideRating",
            "fideId",
            "aicfId",
            "stateId",
            "districtId",
            "association",
            "club",
            "country",
            "state",
            "profileUrl",
            "district",
            "city",
          ],
        },
      ],
    });

    if (!player) {
      return sendResponse(res, 404, {
        success: false,
        error: "Player not found",
      });
    }
    sendResponse(res, 200, {
      success: true,
      data: player,
    });
  } catch (error) {
    handleError(res, error);
  }
};
const getUserProfile = async (req, res) => {
  try {
    const userId = req.user.userId;
    const include = req.query.include;

    // Build query options
    const queryOptions = {
      where: { userId: userId },
      attributes: {
        exclude: ["userId", "createdAt", "updatedAt", "termsAndConditions"],
      },
    };

    // Include related User data if requested
    if (include === "true" || include === true) {
      queryOptions.include = [
        {
          model: User,
          attributes: ["email", "phoneNumber", "cbid", "name"],
        },
      ];
    }

    // Find player profile
    const player = await PlayerDetail.findOne(queryOptions);

    // If player not found, return 404 status code (Not Found)
    if (!player) {
      return sendResponse(res, 204, {
        success: false,
        error: { message: "profile not found" },
      });
    }

    // Player found, return 200 status code with data
    return res.status(200).json({
      success: true,
      data: player.toJSON(),
    });
  } catch (error) {
    console.error("Error fetching user profile:", error);
    return handleError(res, error);
  }
};
const getPlayerTournaments = async (req, res) => {
  try {
    const UserId = req.user.userId;
    const { page = 1, limit = 3, title, status } = req.query;
    const offset = (page - 1) * limit;

    // Fix the variable name
    const whereClause = {
      tournamentStatus: { [Op.ne]: "archived" },
    };

    if (title) whereClause.title = { [Op.iLike]: `%${title}%` };
    if (status) {
      if (status === "upcoming") {
        whereClause.startDate = { [Op.gt]: new Date() };
      } else if (status === "completed") {
        whereClause.endDate = { [Op.lt]: new Date() };
      } else if (status === "in-progress") {
        whereClause.startDate = { [Op.lte]: new Date() };
        whereClause.endDate = { [Op.gte]: new Date() };
      } else {
        whereClause.tournamentStatus = status;
      }
    }

    const { rows: registrations, count: total } =
      await Registration.findAndCountAll({
        where: { playerId: UserId, status: "active" },
        attributes: ["tournamentId", "genderCategory", "ageCategory"],
        include: [
          {
            model: Tournament,
            as: "tournament",
            required: true, // This forces an INNER JOIN instead of LEFT JOIN
            where: whereClause,
            attributes: [
              "title",
              "startDate",
              "endDate",
              "totalCashPrizeCurrency",
              "totalCashPrizeAmount",
              "city",
            ],
          },
        ],
        offset,
        limit,
      });
    if (total === 0) {
      return sendResponse(res, 204, {
        success: true,
        data: {
          tournaments: [],
          total: 0,
          currentPage: page,
          totalPages: 0,
        },
      });
    }

    sendResponse(res, 200, {
      success: true,
      data: {
        tournaments: registrations.map((reg) => ({
          ...reg.tournament.toJSON(),
          ageCategory: reg.ageCategory,
          genderCategory: reg.genderCategory,
        })),
        total,
        currentPage: page,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    handleError(res, error);
  }
};

const getUpcomingTournament = async (req, res) => {
  const UserId = req.user.userId;
  const { page = 1, limit = 3 } = req.query;
  try {
    // Get user details with player information
    const user = await User.findOne({
      where: { id: UserId },
      attributes: ["name"],
      include: [
        {
          model: PlayerDetail,

          attributes: ["gender"],
        },
      ],
    });
    const currentDate = new Date();

    //// First define basic date and status criteria
    const dateConditions = {
      [Op.or]: [
        { startDate: { [Op.gte]: currentDate } },
        {
          startDate: { [Op.lt]: currentDate },
          endDate: { [Op.gte]: currentDate },
        },
        { registrationEndDate: { [Op.gte]: currentDate } },
      ],
    };

    const statusCondition = { tournamentStatus: { [Op.ne]: "archived" } };

    // Then add gender conditions if applicable
    let genderCondition = {};
    if (user?.PlayerDetail?.gender) {
      const { gender } = user.PlayerDetail;

      genderCondition = {
        tournamentCategory: {
          [Op.or]: [
            "open",
            ...(gender === "male" ? ["male"] : []),
            ...(gender === "female" ? ["female"] : []),
          ],
        },
      };
    }

    // Finally, combine all conditions
    const whereClause = {
      ...dateConditions,
      ...statusCondition,
      ...genderCondition,
    };

    const offset = (parseInt(page) - 1) * parseInt(limit);
    const { rows: tournaments, count: total } =
      await Tournament.findAndCountAll({
        attributes: ["id", "title", "tournamentCategory"],
        where: whereClause,
        order: [["createdAt", "DESC"]],
        offset,
        limit: parseInt(limit),
      });

    if (total === 0) {
      sendResponse(res, 404, {
        success: false,
        error: "No eligible tournaments found",
        data: {
          total: 0,
          currentPage: parseInt(page),
          totalPages: 0,
          tournaments: [],
        },
      });
      return;
    }

    sendResponse(res, 200, {
      success: true,
      data: {
        tournaments,
        total,
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / parseInt(limit)),
      },
    });
    return;
  } catch (error) {
    handleError(res, error);
    return;
  }
};
const uploadPlayerFiles = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { name } = req.body;
    const file = req?.file;

    if (!file || !name) {
      return sendResponse(res, 400, {
        success: false,
        error: "File or name is missing",
      });
    }

    const fileUrl = file?.location;
    const fileType = file?.mimetype;
    const fileSize = file?.size;

    const player = await PlayerDetail.findOne({ where: { userId } });

    if (!player) {
      return sendResponse(res, 404, {
        success: false,
        error: "Player not found",
      });
    }

    // Parse existing myFiles or initialize as empty array
    // This properly handles JSONB data from PostgreSQL
    const myFiles = player.myFiles
      ? Array.isArray(player.myFiles)
        ? player.myFiles
        : []
      : [];

    // Create new file object
    const newFile = {
      id: uuidv4(),
      name,
      url: fileUrl,
      fileType,
      size: fileSize,
      createdAt: new Date().toISOString(),
    };

    const newMyFiles = [...myFiles, newFile];

    const [count, rows] = await PlayerDetail.update(
      { myFiles: newMyFiles },
      {
        where: { userId },
        returning: true, // This is the key addition!
      }
    );

    // Check if update was successful
    if (count === 0) {
      return sendResponse(res, 404, {
        success: false,
        error: "Update failed, player not found",
      });
    }

    sendResponse(res, 200, {
      success: true,
      data: rows[0].myFiles,
    });
  } catch (error) {
    deleteFromS3(req?.file?.location);
    console.error("Upload player file error:", error);
    handleError(res, error);
  }
};

// GET /api/documents
const getPlayerDocuments = async (req, res) => {
  try {
    const userId = req.user.userId;

    const player = await PlayerDetail.findOne({ where: { userId } });

    if (!player) {
      return sendResponse(res, 404, {
        success: false,
        error: "Player not found",
      });
    }

    const myFiles = Array.isArray(player.myFiles) ? player.myFiles : [];

    sendResponse(res, 200, {
      success: true,
      data: myFiles,
    });
  } catch (error) {
    console.error("Error fetching player documents:", error);
    handleError(res, error);
  }
};

// DELETE /api/documents/:id (id can be file index or hashed url segment)
const deletePlayerDocument = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { id } = req.params;

    const player = await PlayerDetail.findOne({ where: { userId } });

    if (!player) {
      return sendResponse(res, 404, {
        success: false,
        error: "Player not found",
      });
    }

    const myFiles = Array.isArray(player.myFiles) ? player.myFiles : [];

    const updatedFiles = myFiles.filter((file) => file.id !== id);

    if (updatedFiles.length === myFiles.length) {
      return sendResponse(res, 404, {
        success: false,
        error: "Document not found",
      });
    }

    await player.update({ myFiles: updatedFiles });

    sendResponse(res, 200, {
      success: true,
      message: "Document deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting player document:", error);
    handleError(res, error);
  }
};

// Backend endpoint to generate pre-signed URLs
const documentDownload = async (req, res) => {
  try {
    const documentId = req.params.id;
    const userId = req.user.userId;

    // Verify this user has permission to access this document
    const playerDoc = await PlayerDetail.findOne({
      where: { userId },
      attributes: ["myFiles"],
    });

    if (!playerDoc) {
      return res
        .status(404)
        .json({ success: false, error: "Player not found" });
    }

    const myFiles = playerDoc?.myFiles || [];
    const file = myFiles.find((file) => file?.id === documentId);

    if (!file) {
      return res.status(404).json({ success: false, error: "File not found" });
    }
    const presignedUrl = await generatePresignedUrl(file?.url);

    res.json({ success: true, presignedUrl });
  } catch (error) {
    console.error("Error generating pre-signed URL:", error);
    res
      .status(500)
      .json({ success: false, error: "Failed to generate download URL" });
  }
};

const reportGenerate = async (req, res) => {
  try {
    const { type } = req.query;
    const { rows: details } = await PlayerDetail.findAndCountAll({
      attributes: {
        exclude: [
          "createdAt",
          "updatedAt",
          "id",
          "profileUrl",
          "userId",
          "clubId",
          "termsAndConditions",
        ],
      },
      include: [
        {
          model: User,
          attributes: ["id", "name", "email", "phoneNumber"],
          required: false,
        },
      ],
    });

    if (!details || details.length === 0) {
      return sendResponse(res, 404, {
        success: false,
        error: "Player not found",
      });
    }

    // return;
    const plainDetails = details.map((player) => {
      const plain = player.toJSON();
      const flattened = {
        ...plain,
        userName: plain.User?.name || "",
        userEmail: plain.User?.email || "",
        userPhone: plain.User?.phoneNumber || "",
      };
      delete flattened.User;
      return flattened;
    });

    // return;

    // const newArray = Array.isArray(details)?details:[details]
    let result;
    if (type !== "pdf") {
      result = await exportToExcel({
        data: plainDetails,
        sheetName: "Player_details",
        title: "Player Details Report",
        reportType: "Registered Players",
      });

      if (!result.success) {
        return sendResponse(res, 500, {
          success: false,
          error: result.error,
        });
      }

      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      res.setHeader(
        "Content-Disposition",
        "attachment; filename=player_details_report.xlsx"
      );
    } else {
      result = await exportToPDF({
        data: plainDetails,
        title: "Player Details Report",
      });
      if (!result.success) {
        return sendResponse(res, 500, {
          success: false,
          error: result.error,
        });
      }

      res.set({
        "Content-Type": "application/pdf",
        "Content-Disposition": `attachment; filename=Player_Details.pdf`,
      });
    }

    return res.send(result.buffer);
  } catch (e) {
    handleError(res, e);
    return;
  }
};
const removeProfileImage = async (req, res) => {
  try {
    const userId = req.user.userId;
    if (!userId) {
      return sendResponse(res, 401, {
        success: false,
        error: "Unauthorized",
      });
    }
    const playerDetail = await PlayerDetail.findOne({
      where: { userId: userId },
    });
    if (!playerDetail) {
      return sendResponse(res, 404, {
        success: false,
        error: "Club detail not found",
      });
    }
    if (playerDetail.profileUrl) {
      deleteFromS3(playerDetail.profileUrl);
    }
    playerDetail.profileUrl = null;
    await playerDetail.save();
    return sendResponse(res, 200, {
      success: true,
      message: "Profile image removed successfully",
    });
  } catch (error) {
    handleError(res, error);
  }
};
async function createTournamentRegistrationReminder(req, res) {
  try {
    const { id: tournamentId } = req.params;
    const userId = req.user.userId; // Assuming user is authenticated
    const newTitle = decodeURIComponent(tournamentId);

    // Get tournament details
    const tournament = await Tournament.findOne({
      where: { title: newTitle },
    });
    if (!tournament) {
      return sendResponse(res, 404, {
        success: false,
        error: "Tournament not found",
      });
    }

    // Get user details
    const user = await User.findByPk(userId);
    if (!user) {
      return sendResponse(res, 404, {
        success: false,
        error: "User not found",
      });
    }

    // Check if reminder already exists
    const existingReminder = await Notifications.findOne({
      where: {
        userId: user.id,
        type: "tournament-reminder",
        "content.tournament_id": tournament.id,
        status: { [Op.in]: ["pending", "retry"] },
      },
    });

    if (existingReminder) {
      return sendResponse(res, 409, {
        success: false,
        error: "Reminder already exists",
      });
    }

    // Create notification scheduled for registration opening date
    const notification = await Notifications.create({
      userId: user.id,
      email: user.email,
      type: "tournament-reminder",
      templateId: "tournament-reminder",
      platform: "email",
      status: "pending",
      priority: 2,
      // Set to be active when registration opens
      nextAttemptAt: tournament.registrationStartDate,
      expiresAt: tournament.registrationEndDate,
      maxAttempts: 3,
      deliveryAttempts: 0,
      content: {
        tournament_id: tournament.id,
        tournament_title: tournament.title,
        user_type: "player",
        recipientName: user.name,
        tournamentName: tournament.title,
        registrationDeadline: new Date(
          tournament.registrationEndDate
        ).toLocaleDateString(),
        tournamentStartDate: new Date(
          tournament.startDate
        ).toLocaleDateString(),
        tournamentEndDate: new Date(tournament.endDate).toLocaleDateString(),
        tournamentLocation: tournament.venueAddress,
        registrationUrl: `${process.env.FRONTEND_URL}/tournaments/${tournament.title}`,
      },
    });
    if (!notification) {
      return sendResponse(res, 500, {
        success: false,
        error: "Failed to create tournament reminder",
      });
    }

    return sendResponse(res, 200, {
      success: true,
      data: { message: "Tournament reminder created successfully" },
    });
  } catch (error) {
    console.error("Error creating tournament reminder:", error);
    return handleError(res, error);
  }
}
const getCertifcateDetails = async (req, res) => {
  try {
    const { tournamentTitle: title, page, limit } = req.query;
    const userId = req.user.userId;
    if (!userId) {
      return sendResponse(res, 401, {
        success: false,
        error: "Unauthorized",
      });
    }

    const user = await User.findOne({
      where: { id: userId },
      attributes: ["name"],
    });
    if (!user) {
      return sendResponse(res, 404, {
        success: false,
        error: "User not found",
      });
    }
    const playerName = user.name;

    whereClause = {};

    whereClause.playerName = { [Op.iLike]: `${playerName}` };
    if (title) whereClause.tournamentTitle = { [Op.iLike]: `%${title}%` };

    const { rows: certificates, count } = await Certificate.findAndCountAll({
      where: whereClause,
      attributes: {
        exclude: ["createdAt", "updatedAt", "userId", "tournamentId"],
      },
      offset: (page - 1) * limit,
      limit: parseInt(limit),
      order: [["createdAt", "DESC"]],
    });
    if (!certificates) {
      return sendResponse(res, 404, {
        success: false,
        error: "Certificates not found",
      });
    }
    return sendResponse(res, 200, {
      success: true,
      data: { certificates, total: count },
    });
  } catch (error) {
    handleError(res, error);
  }
};

module.exports = {
  createPlayerProfile,
  editPlayerProfile,
  getUserProfile,
  getAllPlayer,
  getSinglePlayer,
  getPlayerTournaments,
  getUpcomingTournament,
  uploadPlayerFiles,
  getPlayerDocuments,
  deletePlayerDocument,
  documentDownload,
  reportGenerate,
  removeProfileImage,
  createTournamentRegistrationReminder,
  getCertifcateDetails,
};

// In your player profile update controller
const generateCbid = async ({ country_code }) => {
  try {
    const prefix = "CB";
    const currentYear = new Date().getFullYear();
    const yearCode = currentYear.toString().slice(-2);
    const countryCode = country_code || "IN";

    const yearPattern = `${prefix}${yearCode}${countryCode}`;

    // Get the latest user with matching CBID pattern
    const latestUser = await User.findOne({
      where: {
        cbid: {
          [Op.iLike]: `${yearPattern}%`,
        },
      },
      order: [["cbid", "DESC"]],
    });

    let nextSequence = 1;
    if (latestUser && latestUser.cbid) {
      const match = latestUser.cbid.match(/(\d{5})$/);
      if (match) {
        nextSequence = parseInt(match[1], 10) + 1;
      }
    }

    const sequenceNumber = nextSequence.toString().padStart(5, "0");
    return `${prefix}${yearCode}${countryCode}${sequenceNumber}`;
  } catch (error) {
    console.error("Error generating CBID:", error);
    throw error;
  }
};

const sendClubInviteHandler = async ({ userId, clubId, clubName }) => {
  try {
    if (!userId) {
      return { success: false, error: "Unauthorized" };
    }

    if (!clubId || !clubName) {
      return {
        success: false,
        error: "Club id and club name are required",
      };
    }

    const user = await User.findOne({
      where: { id: userId, role: "player" },
      include: [PlayerDetail],
    });

    if (!user || !user.PlayerDetail) {
      return { success: false, error: "User not found" };
    }

    if (user.PlayerDetail.clubId !== null) {
      return {
        success: false,
        error: "You are already in a club, please leave it first",
      };
    }

    const club = await ClubDetail.findOne({ where: { id: clubId } });
    if (!club) {
      return { success: false, error: "Club not found" };
    }

    const [inviteRequest, created] = await InviteRequest.findOrCreate({
      where: {
        userId: club.userId,
        type: "join-request",
        [Op.and]: [
          sequelize.where(
            sequelize.json("metadata.player.playerId"),
            user.PlayerDetail.id
          ),
        ],
      },
      defaults: {
        userId: club.userId,
        type: "join-request",
        title: "club join request",
        message: `${user.name}, has requested to join your club!`,
        metadata: {
          club: { clubName: club.clubName, clubId: club.id },
          player: {
            playerName: user.name,
            playerId: user.PlayerDetail.id,
            userId: user.id,
            cbid: user.cbid,
          },
        },
      },
    });

    if (!created) {
      return { success: false, error: "Join request already sent" };
    }

    await emailService.sendClubJoinRequestEmail({
      email: club.email,
      subject: `${user.name} has requested to join your club!`,
      message: `${user.name}, has requested to join your club!`,
      playerName: user.name,
      clubName: club.clubName,
      joinUrl: `${config.frontend_url}/dashboard`,
    });

    return { success: true };
  } catch (error) {
    return { success: false, error: error.message || "Internal server error" };
  }
};
