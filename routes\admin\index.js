const router = require("express").Router();
const { getContent, updateContent, createContent, } = require("../../controllers/admin/index");

const {
  // getEmailTemplates,
  getSmsTemplates,
  getWhatsappTemplates,
  sendBulkEmail,
  sendSelectiveBulkEmail,
  sendBulkSms,
  sendBulkWhatsapp,
  sendSelectiveBulkSms
} = require("../../controllers/admin/communicationController");

const { getAllClubDetails, getAllArbiter, getAllPlayer } = require("../../controllers/admin/userController");
const verifyJwt = require("../../middlewares/verifyJwt")
const reportRouter = require('./report')
const detailsRouter = require('./details')
const accessRouter = require('./access')
const advertisementRouter = require('./advertisement')


router.use('/access',accessRouter)
router.use('/add',advertisementRouter)

router.get("/content/:slug", verifyJwt, getContent);
router.post("/content", verifyJwt, createContent);
router.put("/content/:slug", verifyJwt, updateContent);


// Template routes
// router.get("/email-templates", verifyJwt, getEmailTemplates);
router.get("/sms-templates", verifyJwt, getSmsTemplates);
router.get("/whatsapp-templates", verifyJwt, getWhatsappTemplates);

// Bulk messaging routes
router.post("/send-email", verifyJwt, sendSelectiveBulkEmail);
router.post("/send-bulk-email", verifyJwt,sendBulkEmail );
router.post("/send-sms", verifyJwt, sendSelectiveBulkSms);
router.post("/send-bulk-sms", verifyJwt, sendBulkSms);
router.post("/send-whatsapp", verifyJwt, sendBulkWhatsapp);
router.use('/report',reportRouter)
router.use('/details',detailsRouter)


router.get("/users/player", verifyJwt, getAllPlayer);
router.get("/users/club", verifyJwt, getAllClubDetails);
router.get("/users/arbiter", verifyJwt, getAllArbiter);

module.exports = router;
