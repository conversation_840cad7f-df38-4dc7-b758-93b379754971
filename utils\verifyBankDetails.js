const { config } = require("../config/config");

async function verifyBankAccount({ account_number, ifsc, name }) {
  // try {
  //   const accessToken = await getPayUToken();

  //   const payload = {
  //     account_number,
  //     ifsc,
  //     name,
  //     name_match_required: true,
  //     leniency: "Medium",
  //     metadata: {
  //       clientId: config.payu_client_id,
  //     },
  //   };

  //   const response = await fetch(
  //     "https://onepayuonboarding.payu.in/dvs/bank_accounts/acc_verification",
  //     {
  //       method: "POST",
  //       headers: {
  //         Authorization: `Bearer ${accessToken}`,
  //         "Content-Type": "application/json",
  //       },
  //       body: JSON.stringify(payload),
  //     }
  //   );

  //   if (!response.ok) {
  //     throw new Error(`HTTP error! status: ${response.status}`);
  //   }

  //   const data = await response.json();
  //   return data;
  // } catch (error) {
  //   console.error("Error verifying bank account:", error);
  //   throw error;
  // }
}

// module.exports = { verifyBankAccount };
