const express = require("express");
const router = express.Router();
const {
  setupClubPayouts,
  createPayout,
  getPayoutStatus,
  getClubPayouts,
  calculatePayoutPreview,
  handleWebhook,
} = require("../../controllers/payoutController");

const verifyJwt = require("../../middlewares/verifyJwt");

router.post("/setup", verifyJwt, setupClubPayouts);
router.post("/create", verifyJwt, createPayout);
router.get("/status/:payout_id", verifyJwt, getPayoutStatus);
router.get("/list", verifyJwt, getClubPayouts);
router.post("/calculate/:tournament_id", verifyJwt, calculatePayoutPreview);
router.post("/webhook", handleWebhook);

module.exports = router;
