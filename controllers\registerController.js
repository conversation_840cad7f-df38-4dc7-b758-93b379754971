const { sendResponse, handleError } = require("../utils/apiResponse");
const {
  Tournament,
  Registration,
  User,
  ClubDetail,
  PlayerDetail,
  BulkRegistration,
  Payment,
} = require("../config/db").models;
const { sequelize } = require("../config/db");

const checkRegistration = async (req, res) => {
  const { userId } = req.user;
  const { title } = req.params;
  const newTitle = decodeURIComponent(title);
  const { tournamentId } = req.query;

  if (!userId || !title) {
    return sendResponse(res, 400, { success: false, error: "Invalid request" });
  }
  try {
    const registration = await Registration.findOne({
      where: {
        playerId: userId,
        tournamentId,
      },
      include: [
        {
          model: Payment,
          as: "payment",
          attributes: ["id", "paymentType"],
          required: false,
        },
        {
          model: Tournament,
          where: { title: newTitle },
          as: "tournament",
          attributes: ["title", "startDate", "endDate", "entryFee"],
        },
      ],
      order: [
        // Priority order: active first, then cancelled, then refunded
        [
          sequelize.literal(`
          CASE 
            WHEN status = 'active' THEN 1 
            WHEN status = 'cancelled' THEN 2 
            WHEN status = 'refunded' THEN 3 
            ELSE 4 
          END
        `),
          "ASC",
        ],
        // If same status, get the most recent one
        ["registeredDate", "DESC"],
        ["updatedAt", "DESC"],
      ],
    });

    if (!registration) {
      return sendResponse(res, 404, {});
    }

    const tournament = registration?.tournament;

    // Determine registration status
    let isRegistered;
    if (registration?.status === "active") {
      isRegistered = "registered";
    } else if (registration?.status === "cancelled") {
      isRegistered = "cancelled";
    } else if (registration?.status === "refunded") {
      isRegistered = "refunded";
    } else {
      isRegistered = "not registered";
    }

    const regType = registration?.payment?.paymentType === "club" ? "club" : "player";

    sendResponse(res, 200, {
      success: true,
      data: {
        isRegistered: isRegistered,
        registrationStatus: registration?.status || null,
        registrationType: regType,
        tournamentDetails: tournament
          ? {
              title: tournament.title,
              startDate: tournament.startDate,
              endDate: tournament.endDate,
              entryFee: tournament.entryFee,
            }
          : null,
        registrationId: registration?.id || null,
      },
    });
  } catch (error) {
    console.error("Error checking registration status:", error);
    handleError(res, error);
  }
};
const attendanceMark = async (req, res) => {
  const {
    attendanceMark,
    registrationId,
    date,
    permanentAttendance,
    tournamentDays,
  } = req.body;
  if (!attendanceMark || !registrationId || !date) {
    return sendResponse(res, 400, {
      success: false,
      error: "registrationId and attendanceMark are required",
    });
  }

  try {
    // Format the date to match the "dayX" structure

    const registration = await Registration.findOne({
      where: { id: registrationId, status: "active" },
      attributes: ["attendanceMark"],
    });

    if (!registration) {
      return sendResponse(res, 404, {
        success: false,
        error: "Registration not found",
      });
    }

    const existingAttendance = registration.attendanceMark || {};
    const updatedAttendance = { ...existingAttendance };

    if (permanentAttendance) {
      // Set the same attendance for all tournamentDays
      for (const day of tournamentDays) {
        const dayDate = day.date;

        // Only set if not already marked
        if (!updatedAttendance[dayDate]) {
          updatedAttendance[dayDate] = {
            day: dayDate,
            attendance: attendanceMark.attendance,
            userId: attendanceMark.userId,
          };
        }
      }
    } else {
      // Only mark for the specific date
      updatedAttendance[attendanceMark.date] = {
        day: attendanceMark.date,
        attendance: attendanceMark.attendance,
        userId: attendanceMark.userId,
      };
    }
    // return;
    const [updateCount, updatedRows] = await Registration.update(
      { attendanceMark: updatedAttendance },
      { where: { id: registrationId }, returning: true }
    );

    if (updateCount === 0) {
      return sendResponse(res, 400, {
        success: false,
        error: "Attendance update failed",
      });
    }

    return sendResponse(res, 200, {
      success: true,
      message: "Attendance updated successfully",
      data: updatedRows[0],
    });
  } catch (error) {
    console.error("Error updating attendance:", error);
    return sendResponse(res, 500, {
      success: false,
      error: "Internal server error",
    });
  }
};
const bulk_Player_Register = async (req, res) => {
  try {
    const { tournamentId, players } = req.body;

    // Check if req.user exists before accessing its properties
    if (!req.user) {
      return sendResponse(res, 401, {
        success: false,
        error: "Authentication required. Please log in.",
      });
    }

    const clubId = req.user.userId;
    if (!clubId) {
      return sendResponse(res, 401, {
        success: false,
        error: "Unauthorized",
      });
    }
    if (!tournamentId || !players) {
      return sendResponse(res, 422, {
        success: false,
        error: "Tournament id and player list is required",
      });
    }
    const tournament = await Tournament.findOne({
      where: { title: tournamentId },
    });
    if (!tournament) {
      return sendResponse(res, 404, {
        success: false,
        error: "Tournament not found",
      });
    }
    // Log the clubId to help with debugging
    const club = await ClubDetail.findOne({
      where: { userId: clubId },
    });

    // Log the result of the club query
    if (!club) {
      return sendResponse(res, 404, {
        success: false,
        error: "Club not found",
      });
    }
    const count = players.length;
    const totalAmount = count * tournament.entryFee;

    // Add await keyword to properly handle the promise
    const newPlayerList = await BulkRegistration.create({
      tournamentId: tournament.id,
      registeredBy: club.id,
      playerList: players,
      totalAmount,
      playersCount: count,
    });

    if (newPlayerList) {
      return sendResponse(res, 200, {
        success: true,
        message: "Players registered successfully",
        data: {
          registrationId: newPlayerList.id,
          bulkRegistrationId: newPlayerList.bulkRegistrationId,
        },
      });
    }
  } catch (error) {
    handleError(res, error);
  }
};

const bulk_Player_get = async (req, res) => {
  try {
    const { id } = req.params;
    if (!id) {
      return sendResponse(res, 422, {
        success: false,
        error: "registrationId is required",
      });
    }
    const bulkRegistration = await BulkRegistration.findOne({
      where: { bulk_registration_id: id },
      attributes: ["playerList", "id"],
    });

    if (!bulkRegistration || !bulkRegistration.playerList) {
      return res.status(404).json({ message: "No players found" });
    }

    const playerIds = bulkRegistration.playerList.map((p) => p.playerId);

    const players = await User.findAll({
      where: { id: playerIds },
      attributes: ["id", "name", "email", "cbid"],
      include: [
        {
          model: PlayerDetail,
          attributes: [
            "dob",
            "gender",
            "fideId",
            "aicfId",
            "stateId",
            "districtId",
            "playerTitle",
          ],
        },
      ],
    });

    const enrichedPlayerList = bulkRegistration.playerList.map((player) => {
      const user = players.find((u) => u.id === player.playerId);
      return {
        ...player,
        ...user?.dataValues,
        ...user?.PlayerDetail?.dataValues,
      };
    });

    sendResponse(res, 200, {
      success: true,
      data: {
        players: enrichedPlayerList,
        bulkRegistrationId: bulkRegistration.id,
      },
    });
  } catch (error) {
    handleError(res, error);
  }
};

const getPendingBulkRegisters = async (req, res) => {
  try {
    const { title } = req.params;
    const tournamentId = decodeURIComponent(title);
    if (!tournamentId) {
      return sendResponse(res, 422, {
        success: false,
        error: "Tournament id is required",
      });
    }

    const clubId = req.user.userId;
    if (!clubId) {
      return sendResponse(res, 401, {
        success: false,
        error: "Unauthorized",
      });
    }

    const club = await ClubDetail.findOne({
      where: { userId: clubId },
      attributes: ["clubName", "id"],
    });
    if (!club) {
      return sendResponse(res, 404, {
        success: false,
        error: "Club not found",
      });
    }
    const tournament = await Tournament.findOne({
      where: { title: tournamentId },
    });
    if (!tournament) {
      return sendResponse(res, 404, {
        success: false,
        error: "Tournament not found",
      });
    }

    const registrations = await BulkRegistration.findOne({
      where: {
        registeredBy: club.id,
        registrationStatus: "pending",
        tournamentId: tournament.id,
      },
    });
    if (!registrations) {
      return sendResponse(res, 204, {
        success: true,
        message: "No pending registrations found",
      });
    }
    sendResponse(res, 200, {
      success: true,
      data: registrations,
    });
  } catch (error) {
    console.error("Error in getPendingBulkRegisters:", error);
    handleError(res, error);
  }
};
const updateBulkRegister = async (req, res) => {
  try {
    const { bulkRegistrationId, players, tournamentId } = req.body;
    if (!tournamentId || !players || !bulkRegistrationId) {
      return sendResponse(res, 422, {
        success: false,
        error: "registrationId and registrationStatus are required",
      });
    }
    const tournament = await Tournament.findOne({
      where: { title: tournamentId },
    });
    const registration = await BulkRegistration.findOne({
      where: { bulkRegistrationId, tournamentId: tournament.id },
    });
    if (!registration) {
      return sendResponse(res, 404, {
        success: false,
        error: "Registration not found",
      });
    }
    const count = players.length;
    const totalAmount = count * tournament.entryFee;
    await registration.update({
      playerList: players,
      playersCount: count,
      totalAmount,
    });
    sendResponse(res, 200, {
      success: true,
      message: "Registration status updated successfully",
    });
  } catch (error) {
    handleError(res, error);
  }
};

module.exports = {
  checkRegistration,
  attendanceMark,
  bulk_Player_Register,
  getPendingBulkRegisters,
  updateBulkRegister,
  bulk_Player_get,
};
