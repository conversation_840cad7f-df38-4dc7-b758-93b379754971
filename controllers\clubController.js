const {
  ClubDetail,
  Bankdetails,
  Registration,
  User,
  PlayerDetail,
  BulkRegistration,
  Payment,
  Tournament,
  InviteRequest,
} = require("../config/db").models;
const { Op, Sequelize } = require("sequelize");
const { clubDetailSchema, getAllClubSchema } = require("../schema/clubSchema");
const { sendResponse, handleError } = require("../utils/apiResponse");
const { bankDetailsSchema } = require("../schema/bankSchema");
const { verifyBankAccount } = require("../utils/verifyBankDetails");
const playerDetail = require("../models/playerDetail");
const emailService = require("../utils/mailer/emailService");
const { z } = require("zod");
const { config } = require("../config/config");
const { deleteFromS3 } = require("../utils/s3");
const { exportToExcel, exportToPDF } = require("../utils/report-generation");

/** * Create a new club detail
 * @param {import('express').Request} req - Express request object *
 * @param {import('express').Response} res - Express response object
 */
const createClubProfile = async (req, res) => {
  const userId = req.user.userId;
  if (!userId) {
    return sendResponse(res, 403, {
      success: false,
      error: "Forbidden",
    });
  }
  try {
    const { success, data, error } = clubDetailSchema.safeParse(req.body);
    if (!success) {
      sendResponse(res, 422, {
        success: false,
        error: "Validation failed",
        data: error.format(),
      });
      return;
    }
    const clubId = data.clubName.toLocaleLowerCase().replace(/\s+/g, "-");
    const clubDetail = await ClubDetail.create({
      ...data,
      profileUrl: req?.file ? req?.file?.location : null,
      userId: userId,
      clubId: clubId,
    });

    // Remove id, createdAt, and updatedAt from the response
    const { id, createdAt, updatedAt, ...clubData } = clubDetail.toJSON();

    sendResponse(res, 201, {
      success: true,
      message: "Club detail created successfully",
      data: clubData,
    });
  } catch (error) {
    deleteFromS3(req?.file?.location);
    handleError(res, error);
  }
};
/**
 * Edit an existing club detail *
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object */

const editClubProfile = async (req, res) => {
  const { userId } = req.user || {};
  if (!userId) {
    return sendResponse(res, 403, {
      success: false,
      error: "Forbidden",
    });
  }

  try {
    const { success, data, error } = clubDetailSchema
      .partial()
      .safeParse(req.body);
    if (!success) {
      return sendResponse(res, 422, {
        success: false,
        error: "Validation failed",
        data: error.format(),
      });
    }

    const existingClubDetail = await ClubDetail.findOne({
      where: { userId },
    });
    if (!existingClubDetail) {
      return sendResponse(res, 404, {
        success: false,
        error: "Club detail not found",
      });
    }
    const profileUrl = req?.file
      ? req?.file?.location
      : existingClubDetail.profileUrl || null;

    const [count, rows] = await ClubDetail.update(
      { profileUrl, ...data },
      {
        where: { userId },
        returning: true,
      }
    );

    if (
      (count !== 0 || rows.length !== 0) &&
      existingClubDetail?.profileUrl !== profileUrl
    ) {
      deleteFromS3(existingClubDetail?.profileUrl);
    }

    if (count === 0 || rows.length === 0) {
      return sendResponse(res, 404, {
        success: false,
        error: "Club detail not found",
      });
    }

    const { id, createdAt, updatedAt, ...clubData } =
      rows[0].toJSON?.() || rows[0];

    return sendResponse(res, 200, {
      success: true,
      message: "Club detail updated successfully",
      data: clubData,
    });
  } catch (error) {
    console.error(error);
    deleteFromS3(req?.file?.location);
    handleError(res, error);
  }
};

/**
 * Get a club detail by ID
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */

const getClubProfile = async (req, res) => {
  const userId = req.user.userId;
  if (!userId) {
    return sendResponse(res, 403, {
      success: false,
      error: "forbidden",
    });
  }
  try {
    const clubDetails = await ClubDetail.findOne({ where: { userId: userId } });
    if (!clubDetails) {
      return sendResponse(res, 204, {
        success: false,
        error: "Club detail not found",
      });
    }
    // Remove id, createdAt, and updatedAt from the response
    const { createdAt, updatedAt, ...clubData } = clubDetails.toJSON();

    return sendResponse(res, 200, {
      success: true,
      data: clubData,
    });
  } catch (error) {
    handleError(res, error);
  }
};

/**
 * Get all club details
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */

const getAllClubDetails = async (req, res) => {
  const { data, success, error } = getAllClubSchema.safeParse(req.query);

  if (!success) {
    return sendResponse(res, 422, {
      success: false,
      error: "Invalid query parameters",
    });
  }
  try {
    const {
      page = 1,
      limit = 10,
      clubName = "",
      country = "",
      state = "",
      district = "",
      city = "",
    } = data;

    const offset = (page - 1) * limit;
    const whereClause = {};
    const filterFields = { clubName, country, state, district, city };
    Object.entries(filterFields).forEach(([key, value]) => {
      if (value) whereClause[key] = { [Op.iLike]: `%${value}%` };
    });

    const { rows: clubs, count } = await ClubDetail.findAndCountAll({
      where: whereClause,
      offset,
      limit,
      attributes: { exclude: ["id", "createdAt", "updatedAt"] },
      order: [["clubName", "ASC"]],
    });

    const response = {
      clubs,
      total: count,
      currentPage: page,
      totalPages: Math.ceil(count / limit),
    };
    sendResponse(res, 200, {
      success: true,
      data: response,
    });
  } catch (error) {
    handleError(res, error);
  }
};

/**
 * Get a single club detail by ID
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */

const getClubDetailById = async (req, res) => {
  try {
    const { id } = req.params;
    if (!id || typeof id !== "string") {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid club ID",
      });
    }
    const clubDetail = await ClubDetail.findOne({
      where: { clubId: id },
      attributes: { exclude: ["id", "userId", "createdAt", "updatedAt"] },
    });

    if (clubDetail) {
      const {
        clubName,
        clubDistrictId,
        clubId,
        country,
        state,
        district,
        city,
        address,
        locationUrl,
        profileUrl,
        contactPersonName,
        contactPersonNumber,
        contactPersonEmail,
        alternateContactNumber,
      } = clubDetail.toJSON();
      const filteredClubDetail = {
        clubName,
        clubDistrictId,
        country,
        state,
        clubId,
        district,
        city,
        address,
        locationUrl,
        profileUrl,
        contactPersonName,
        contactPersonNumber,
        contactPersonEmail,
        alternateContactNumber,
      };
      clubDetail.dataValues = filteredClubDetail;
    }

    if (!clubDetail) {
      return sendResponse(res, 404, {
        success: false,
        error: "Club detail not found",
      });
    }

    // Add additional information or related data if needed
    // For example, you could include the number of members or recent tournaments

    return sendResponse(res, 200, {
      success: true,
      data: clubDetail,
    });
  } catch (error) {
    console.error("Error in getClubDetailById:", error);
    handleError(res, error);
  }
};

const getRegisteredPlayers = async (req, res) => {
  try {
    const { title } = req.params;

    if (!title) {
      return sendResponse(res, 422, {
        success: false,
        error: "Invalid tournament title",
      });
    }
    const newTitle = decodeURIComponent(title);

    // Destructure and validate query parameters
    const {
      page = 1,
      limit = 5,
      playerName,
      playerId,
      ageCategory,
      genderCategory,
      tournamentId,
      registerId,
    } = req.query;
    // Build player details where clause
    const whereClause = {};

    

    // Build user where clause
    const userWhereClause = {};

    // Player ID filtering
    if (playerId) {
      if (playerId.toLowerCase().startsWith("cb")) {
        userWhereClause.cbid = { [Op.iLike]: `%${playerId}%` };
      } else {
        whereClause[Op.or] = [
          { fideId: { [Op.iLike]: `%${playerId}%` } },
          { aicfId: { [Op.iLike]: `%${playerId}%` } },
          { stateId: { [Op.iLike]: `%${playerId}%` } },
          { districtId: { [Op.iLike]: `%${playerId}%` } },
        ];
      }
    }

    // Player name filtering
    if (playerName) {
      userWhereClause.name = { [Op.iLike]: `%${playerName}%` };
    }

    const registerWhereClause = { status: "active" };

    if (registerId) {
      registerWhereClause.regId = registerId;
    }

    // Gender filtering
    if (genderCategory === "male" || genderCategory === "female") {
      registerWhereClause.genderCategory = genderCategory;
    }

    // Age category filtering
    if (ageCategory && ageCategory.startsWith("U")) {
        registerWhereClause.ageCategory = ageCategory;
    }

    // Tournament where clause
    const tournamentWhereClause = {
      title: newTitle,
    };

    const offset = (page - 1) * limit;

    // Execute query with proper includes
    const { rows: registrations, count: total } =
      await Registration.findAndCountAll({
        where: { tournamentTitle: newTitle, ...registerWhereClause },
        offset,
        limit: limit,
        include: [
          {
            model: User,
            as: "player",
            where: userWhereClause,
            attributes: ["cbid", "name",'email','phoneNumber'],
            include: [
              {
                model: PlayerDetail,
                where: whereClause,
                attributes: [
                  "playerTitle",
                  "fideRating",
                  "fideId",
                  "aicfId",
                  "districtId",
                  "stateId",
                  "club",
                ],
                required: true,
              },
            ],
            required: true,
          },
          {
            model: Tournament,
            as: "tournament",
            where: tournamentWhereClause,
            attributes: ["title", "startDate", "endDate"],
            required: true,
          },
        ],
      });

    // Handle empty results
    if (total === 0 || registrations.length === 0) {
      return sendResponse(res, 200, {
        // Changed from 204 to 200 for consistency
        success: true,
        data: {
          players: [],
          total: 0,
          currentPage: page,
          totalPages: 0,
        },
      });
    }

    // Format registrations
    const formattedRegistrations = registrations.map((reg) => ({
      cbid: reg.player?.cbid,
      playerName: reg.player?.name,
      name: reg.player?.name,
      phone:reg.player?.phoneNumber,
      email:reg.player?.email,
      fideRating: reg.player?.PlayerDetail?.fideRating,
      fideId: reg.player?.PlayerDetail?.fideId,
      aicfId: reg.player?.PlayerDetail?.aicfId,
      stateId: reg.player?.PlayerDetail?.stateId,
      club: reg.player?.PlayerDetail?.club,
      tournamentTitle: reg.tournament?.title,
      attendanceMark: reg.attendanceMark,
      districtId: reg.player?.PlayerDetail?.districtId,
      playerTitle: reg.player?.PlayerDetail?.playerTitle,
      registrationId: reg.id,
      startDate: reg.tournament?.startDate,
      endDate: reg.tournament?.endDate,
    }));

    // Return success response
    return sendResponse(res, 200, {
      success: true,
      data: {
        players: formattedRegistrations,
        total,
        currentPage: page,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Error getting registered players:", error);
    return handleError(res, error);
  }
};

const clubEnquiry = async (req, res) => {
  try {
    const { email, query, mobile, name, subject, clubName, Mail } = req.body;

    await emailService.sendClubEnquiryEmail({
      Mail,
      email,
      query,
      mobile,
      name,
      subject,
      clubName,
    });
    return sendResponse(res, 200, {
      success: true,
      data: {
        message: "Enquiry sent successfully",
      },
    });
  } catch (error) {
    handleError(res, error);
  }
};
const clubInviteSchema = z.object({
  playerName: z.string().min(1, "Player name is required"),
  playerId: z.string().min(1, "CBID is required"),
  message: z.string().min(1, "Message is required"),
});

const clubInvite = async (req, res) => {
  try {
    const { success, data, error } = clubInviteSchema.safeParse(req.body);
    const clubId = req.user.userId;
    const clubEmail = req.user.email;

    // Validate input
    if (!success) {
      return sendResponse(res, 422, {
        success: false,
        error: error.errors,
      });
    }
    const { playerName, playerId: cbid, message } = data;

    // Find the player
    const player = await User.findOne({ where: { cbid } });
    if (!player) {
      return sendResponse(res, 404, {
        success: false,
        error: "Player not found",
      });
    }
    const club = await ClubDetail.findOne({
      where: { userId: clubId },
      attributes: ["clubName", "id", "clubId"],
    });

    const [notification, created] = await InviteRequest.findOrCreate({
      where: {
        userId: player.id,
        type: "club-invite",
        [Op.and]: [
          Sequelize.where(
            Sequelize.json("metadata.club.clubId"), // 🧠 access inside JSON
            club.id // 🧠 compare value
          ),
        ],
      },
      defaults: {
        userId: player.id,
        type: "club-invite",
        title: "Club Invitation",
        message: `${playerName}, you've been invited to join a club!`,
        metadata: {
          club: {
            clubName: club.clubName,
            clubId: club.id,
            clubTitle: club.clubId,
            clubEmail: clubEmail,
          },
          message,
        },
      },
    });
    if (!created) {
      return sendResponse(res, 409, {
        success: false,
        error: "Player already invited",
      });
    }

    // Send email
    emailService.sendClubInviteEmail({
      email: player.email,
      subject: `${playerName}, you've been invited to join a club!`,
      message,
      clubName: club.clubName,
      joinUrl: `${config.frontend_url}/dashboard`,
    });

    return sendResponse(res, 200, {
      success: true,
      message: "Club invitation sent successfully",
    });
  } catch (error) {
    handleError(res, error);
  }
};

const getClubJoinRequest = async (req, res) => {
  try {
    const clubId = req.user.userId;
    if (!clubId) {
      return sendResponse(res, 401, {
        success: false,
        error: "Unauthorized",
      });
    }
    const joinRequests = await InviteRequest.findAll({
      where: {
        type: "join-request",
        userId: clubId,
      },
    });
    if (joinRequests.length === 0) {
      return sendResponse(res, 204, {
        success: false,
        error: "No join requests found",
      });
    }
    sendResponse(res, 200, {
      success: true,
      data: joinRequests,
    });
  } catch (error) {
    handleError(res, error);
  }
};

const updateClubJoinRequest = async (req, res) => {
  try {
    const { action, playerId } = req.body;
    const clubId = req.user.userId;
    if (!clubId) {
      return sendResponse(res, 401, {
        success: false,
        error: "Unauthorized",
      });
    }

    if (!playerId) {
      return sendResponse(res, 422, {
        success: false,
        error: "Player id is required",
      });
    }
    const club = await ClubDetail.findOne({ where: { userId: clubId } });

    const joinRequest = await InviteRequest.findOne({
      where: {
        type: "join-request",
        userId: clubId,
        [Op.and]: [
          Sequelize.where(Sequelize.json("metadata.player.playerId"), playerId),
        ],
      },
    });
    if (!joinRequest) {
      return sendResponse(res, 204, {
        success: false,
        error: "No join request found",
      });
    }
    const player = await PlayerDetail.findOne({
      where: { userId: joinRequest.metadata.player.userId },
    });
    if (!player) {
      return sendResponse(res, 404, {
        success: false,
        error: "Player not found",
      });
    }
    if (action === "reject") {
      await joinRequest.destroy();
      return sendResponse(res, 200, {
        success: true,
        message: "Join request rejected",
      });
    }
    if (action === "accept") {
      if (player.clubId !== null) {
        return sendResponse(res, 409, {
          success: false,
          error: "Player already in a club",
        });
      }
      player.clubId = club.id;
      player.club = club.clubName;
      await player.save();
      emailService.sendClubAcceptRequestEmail({
        email: joinRequest.metadata.player.email,
        subject: `${club.clubName} has accepted your request to join`,
        playerName: player.name,
        clubName: club.clubName,
        clubProfileUrl: `${config.frontend_url}/dashboard/club/${club.clubId}`,
      });
      await joinRequest.destroy();
      return sendResponse(res, 200, {
        success: true,
        message: "Join request accepted",
      });
    }
  } catch (error) {
    handleError(res, error);
  }
};

// get all players for tournaemnts controllers

const getAllClubPlayersForTournament = async (req, res) => {
  try {
    const clubId = req.user.userId;
    const {
      page = 1,
      limit = 5,
      playerName,
      playerId,
      ageCategory,
      genderCategory,
      excludeCbids,
      tournamentId, // New parameter to get the tournament ID
    } = req.query;
    if (!clubId) {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid club ID",
      });
    }

    const whereClause = {};

    // if (genderCategory && genderCategory === "male")
    //   whereClause.gender = "male";
    if (genderCategory && genderCategory === "female")
      whereClause.gender = "female";

    // For age category filtering
    if (ageCategory && ageCategory.startsWith("U")) {
      const ageLimit = parseInt(ageCategory.substring(1));
      if (!isNaN(ageLimit)) {
        const today = new Date();
        const birthDateCutoff = new Date(
          today.getFullYear() - ageLimit,
          today.getMonth(),
          today.getDate()
        );
        whereClause.dob = { [Op.gte]: birthDateCutoff };
      }
    }

    if (playerId && !playerId.toLowerCase().startsWith("cb")) {
      whereClause[Op.or] = [
        { fideId: { [Op.iLike]: `%${playerId}%` } },
        { aicfId: { [Op.iLike]: `%${playerId}%` } },
        { stateId: { [Op.iLike]: `%${playerId}%` } },
        { districtId: { [Op.iLike]: `%${playerId}%` } },
      ];
    }

    let userWhereClause = {};
    if (playerId && playerId.toLowerCase().startsWith("cb")) {
      userWhereClause = { cbid: { [Op.iLike]: `%${playerId}%` } };
    }
    if (playerName) userWhereClause.name = { [Op.iLike]: `%${playerName}%` };
    if (excludeCbids) {
      userWhereClause[Op.not] = { cbid: { [Op.in]: excludeCbids.split(",") } };
    }
    const tournament = await Tournament.findOne({
      where: { title: tournamentId },
      attributes: ["title", "clubId", "id"],
    });
    const club = await ClubDetail.findOne({
      where: { userId: clubId },
      attributes: ["clubName", "id"],
    });

    if (!club) {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid club ID",
      });
    }

    // NEW CODE: Get all players already registered for this tournament
    if (tournament) {
      // Find player IDs of successfully registered players (with completed payment)
      const registeredPlayerIds = await Registration.findAll({
        where: {
          tournamentId: tournament.id,
          status: "active",
        },
        attributes: ["playerId"],
        include: [
          {
            model: User,
            as: "player", // Added the alias to fix the EagerLoadingError
            attributes: ["id", "name"],
            include: [
              {
                model: PlayerDetail,
                where: { clubId: club.id },
                attributes: ["id"],
              },
            ],
          },
        ],
      }).then((registrations) => registrations.map((reg) => reg.playerId));
      // Also find player IDs from bulk registrations that are in "registered"

      // If we have registered players, exclude them
      if (registeredPlayerIds.length > 0) {
        // If there's already a condition on 'id', we need to handle it differently
        if (userWhereClause.id) {
          // If id is already an Op.notIn condition
          if (userWhereClause.id[Op.notIn]) {
            userWhereClause.id[Op.notIn] = [...userWhereClause.id[Op.notIn]];
          } else {
            // If id has a different condition, create an AND condition
            const existingIdCondition = userWhereClause.id;
            userWhereClause.id = {
              [Op.and]: [
                existingIdCondition,
                { [Op.notIn]: registeredPlayerIds },
              ],
            };
          }
        } else {
          // Simple case: just add the notIn condition
          userWhereClause.id = { [Op.notIn]: registeredPlayerIds };
        }
      }
    }

    whereClause.clubId = club.id;

    const { count, rows: players } = await PlayerDetail.findAndCountAll({
      where: whereClause,
      offset: (page - 1) * limit,
      limit: parseInt(limit),
      attributes: [
        "playerTitle",
        "id",
        "userId",

        "fideRating",
        "fideId",
        "aicfId",
        "districtId",
        "dob",
        "stateId",
        "country",
        "state",
        "district",
        "city",
      ],
      include: [
        {
          model: User,
          where: userWhereClause,
          attributes: ["cbid", "name"],
          required: true,
        },
      ],
      order: [["fideRating", "DESC"]],
    });

    const formattedPlayers = players.map((player) => {
      const playerData = player.toJSON();
      if (player.User) {
        return { ...playerData, ...player.User.toJSON() };
      }
      return playerData;
    });

    const response = {
      players: formattedPlayers,
      total: count,
      currentPage: parseInt(page),
      totalPages: Math.ceil(count / parseInt(limit)),
    };

    sendResponse(res, 200, {
      success: true,
      data: response,
    });
  } catch (error) {
    console.error("Error in getAllClubPlayersForTournament:", error);
    handleError(res, error);
  }
};
const removePlayer = async (req, res) => {
  try {
    const { playerId } = req.body;
    const clubId = req.user.userId;
    if (!clubId) {
      return sendResponse(res, 401, {
        success: false,
        error: "Unauthorized",
      });
    }
    if (!playerId) {
      return sendResponse(res, 422, {
        success: false,
        error: "Player id is required",
      });
    }
    const player = await User.findOne({
      where: { cbid: playerId },
      include: [PlayerDetail],
    });
    if (!player) {
      return sendResponse(res, 404, {
        success: false,
        error: "Player not found",
      });
    }

    player.PlayerDetail.clubId = null;
    player.PlayerDetail.club = null;
    await player.PlayerDetail.save();
    emailService.sendMemberRemovedEmail({
      email: player.email,
      subject: `You have been removed from ${player.PlayerDetail.club}`,
      playerName: player.name,
      clubName: player.PlayerDetail.club,
      reason: req.body.reason || "",
    });
    return sendResponse(res, 200, {
      success: true,
      message: "Player removed successfully",
    });
  } catch (error) {
    handleError(res, error);
  }
};

const reportGenerate = async (req, res) => {
  try {
    const { type } = req.query;
    const { rows: details } = await ClubDetail.findAndCountAll({
      attributes: {
        exclude: [
          "createdAt",
          "updatedAt",
          "id",
          "profileUrl",
          "userId",
          "clubId",
        ],
      },
      include: [
        {
          model: User,
          as: "user",
          attributes: ["id", "name", "email", "phoneNumber"],
          required: false,
        },
      ],
    });

    if (!details || details.length === 0) {
      return sendResponse(res, 404, {
        success: false,
        error: "Club not found",
      });
    }
  
    // return;
    const plainDetails = details.map((club) => {
      const plain = club.toJSON();
      const flattened = {
        ...plain,
        userName: plain.user?.name || "",
        userEmail: plain.user?.email || "",
        userPhone: plain.user?.phoneNumber || "",
      };
      delete flattened.user;
      return flattened;
    });


    // return;

    // const newArray = Array.isArray(details)?details:[details]
    let result;
    if (type !== "pdf") {
      result = await exportToExcel({
        data: plainDetails,
        sheetName: "Club_details",
        title: "Club Details Report",
        reportType: "Registered Clubs",
      });

      if (!result.success) {
        return sendResponse(res, 500, {
          success: false,
          error: result.error,
        });
      }

      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      res.setHeader(
        "Content-Disposition",
        "attachment; filename=club_details_report.xlsx"
      );
    } else {
      result = await exportToPDF({
        data: plainDetails,
        title: "Club Details Report",
      });
      if (!result.success) {
        return sendResponse(res, 500, {
          success: false,
          error: result.error,
        });
      }

      res.set({
        "Content-Type": "application/pdf",
        "Content-Disposition": `attachment; filename=Club_Details.pdf`,
      });
    }

    return res.send(result.buffer);
  } catch (e) {
    handleError(res, e);
    return;
  }
};

const removeProfileImage = async (req, res) => {
  try {
    const userId = req.user.userId;
    if (!userId) {
      return sendResponse(res, 401, {
        success: false,
        error: "Unauthorized",
      });
    }
    const clubDetail = await ClubDetail.findOne({ where: { userId: userId } });
    if (!clubDetail) {
      return sendResponse(res, 404, {
        success: false,
        error: "Club detail not found",
      });
    }
    if (clubDetail?.profileUrl) {
      deleteFromS3(clubDetail?.profileUrl);
    }
    clubDetail.profileUrl = null;
    await clubDetail.save();
    return sendResponse(res, 200, {
      success: true,
      message: "Profile image removed successfully",
    });
  } catch (error) {
    handleError(res, error);
  }
};

const getAllClubs = async (req, res) => {
  try {
    const { id: search } = req.params;
    let whereClause = {};

    if (typeof search === "string" && search.trim()) {
      whereClause.clubName = { [Op.iLike]: `%${search.trim()}%` };
    }

    const { rows: details, count } = await ClubDetail.findAndCountAll({
      where: whereClause,
      attributes: ["id", "clubName", "clubId"],
      limit: 10,
    });

    if (!details || details.length === 0) {
      return sendResponse(res, 404, {
        success: false,
        error: "clubs not found",
      });
    }

    sendResponse(res, 200, {
      success: true,
      data: details,
      total: count,
    });
  } catch (error) {
    handleError(res, error);
  }
};

module.exports = {
  createClubProfile,
  editClubProfile,
  getClubProfile,
  getAllClubDetails,
  getClubDetailById,
  getRegisteredPlayers,
  clubEnquiry,
  clubInvite,
  getClubJoinRequest,
  updateClubJoinRequest,
  getAllClubPlayersForTournament,
  removePlayer,
  reportGenerate,
  removeProfileImage,
  getAllClubs,
};
