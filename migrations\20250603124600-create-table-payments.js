'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Create ENUM types first
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_payments_payment_status" AS ENUM ('created', 'pending', 'authorized', 'captured', 'failed', 'refunded');`
    );
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_payments_payment_type" AS ENUM ('player', 'club');`
    );
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_payments_razorpay_order_status" AS ENUM ('created', 'attempted', 'paid');`
    );

    await queryInterface.createTable("payments", {
      id: {
        type: Sequelize.UUID,
        allowNull: false,
        primaryKey: true,
        defaultValue: Sequelize.UUIDV4,
      },
      user_id: {
        type: Sequelize.UUID,
        allowNull: false,
      },
      tournament_id: {
        type: Sequelize.UUID,
        allowNull: false,
      },
      registration_id: {
        type: Sequelize.UUID,
        allowNull: true,
      },
      bulk_registration_id: {
        type: Sequelize.UUID,
        allowNull: true,
      },
      razorpay_order_id: {
        type: Sequelize.STRING,
        allowNull: true,
        unique: true,
      },
      razorpay_payment_id: {
        type: Sequelize.STRING,
        allowNull: true,
        unique: true,
      },
      razorpay_signature: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      payment_date: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn("NOW"),
      },
      payment_status: {
        type: "enum_payments_payment_status",
        allowNull: false,
        defaultValue: "created",
      },
      payment_transaction_id: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      payment_amount: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      payment_currency: {
        type: Sequelize.STRING(3),
        allowNull: false,
        defaultValue: "INR",
      },
      payment_method: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      payment_reference: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      payment_type: {
        type: "enum_payments_payment_type",
        allowNull: false,
      },
      payment_remarks: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      razorpay_response: {
        type: Sequelize.JSONB,
        allowNull: true,
      },
      razorpay_order_status: {
        type: "enum_payments_razorpay_order_status",
        allowNull: true,
      },
      razorpay_attempts: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn("NOW"),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn("NOW"),
      },
    });

    // Create indexes
    await queryInterface.addIndex("payments", ["razorpay_order_id"]);
    await queryInterface.addIndex("payments", ["razorpay_payment_id"]);
    await queryInterface.addIndex("payments", ["payment_status"]);
    await queryInterface.addIndex("payments", ["user_id", "tournament_id"]);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable("payments");
    await queryInterface.sequelize.query(
      `DROP TYPE IF EXISTS "enum_payments_payment_status";`
    );
    await queryInterface.sequelize.query(
      `DROP TYPE IF EXISTS "enum_payments_payment_type";`
    );
    await queryInterface.sequelize.query(
      `DROP TYPE IF EXISTS "enum_payments_razorpay_order_status";`
    );
  },
};