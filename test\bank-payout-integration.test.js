const request = require('supertest');
const app = require('../app'); // Adjust path to your app
const { sequelize } = require('../config/db');

describe('Bank Verification and Payout Integration Tests', () => {
  let authToken;
  let clubId;
  let tournamentId;
  let bankDetailsId;
  let payoutId;

  // Test data
  const testClubData = {
    name: 'Test Chess Club',
    email: '<EMAIL>',
    phone: '**********',
    address: 'Test Address, Test City',
    description: 'Test club for integration testing'
  };

  const testBankData = {
    bankName: 'State Bank of India',
    AccountNumber: '**************',
    branchIFSCCode: 'SBIN0001234',
    branchName: 'Test Branch',
    bankAccountType: 'current',
    bankAccountHolderName: 'TEST CHESS CLUB'
  };

  const testTournamentData = {
    name: 'Test Tournament',
    entryFee: 500,
    participantCount: 20,
    totalCollected: 10000,
    status: 'completed'
  };

  beforeAll(async () => {
    // Setup test database
    await sequelize.sync({ force: true });
    
    // Create test user/club and get auth token
    const registerResponse = await request(app)
      .post('/api/auth/register')
      .send({
        name: testClubData.name,
        email: testClubData.email,
        password: 'testpassword123',
        role: 'club',
        phoneNumber: testClubData.phone
      });

    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        email: testClubData.email,
        password: 'testpassword123'
      });

    authToken = loginResponse.body.data.token;
    clubId = loginResponse.body.data.user.id;

    // Create club profile
    await request(app)
      .post('/api/club/profile')
      .set('Authorization', `Bearer ${authToken}`)
      .send(testClubData);

    // Create test tournament
    const tournamentResponse = await request(app)
      .post('/api/tournament')
      .set('Authorization', `Bearer ${authToken}`)
      .send(testTournamentData);
    
    tournamentId = tournamentResponse.body.data.id;
  });

  afterAll(async () => {
    await sequelize.close();
  });

  describe('Bank Verification Flow', () => {
    test('1. Create banking details with RazorPay setup', async () => {
      const response = await request(app)
        .post('/api/club/bank/create')
        .set('Authorization', `Bearer ${authToken}`)
        .send(testBankData);

      console.log('Bank Creation Response:', JSON.stringify(response.body, null, 2));

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('setup_details');
      expect(response.body.data.setup_details).toHaveProperty('contact_id');
      expect(response.body.data.setup_details).toHaveProperty('fund_account_id');
      
      bankDetailsId = response.body.data.id;
    });

    test('2. Get banking details', async () => {
      const response = await request(app)
        .get('/api/club/bank/details')
        .set('Authorization', `Bearer ${authToken}`);

      console.log('Bank Details Response:', JSON.stringify(response.body, null, 2));

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('verification_details');
      expect(response.body.data.AccountNumber).toMatch(/\*+/); // Should be masked
    });

    test('3. Check verification status', async () => {
      const response = await request(app)
        .get('/api/club/bank/verify-status')
        .set('Authorization', `Bearer ${authToken}`);

      console.log('Verification Status Response:', JSON.stringify(response.body, null, 2));

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('verification_status');
    });

    test('4. Toggle payout status', async () => {
      const response = await request(app)
        .post('/api/club/bank/toggle-payout')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ enable: true });

      console.log('Toggle Payout Response:', JSON.stringify(response.body, null, 2));

      // This might fail if bank is not verified, which is expected
      if (response.status === 200) {
        expect(response.body.success).toBe(true);
        expect(response.body.data.payout_enabled).toBe(true);
      } else {
        expect(response.status).toBe(400);
        expect(response.body.error).toContain('unverified');
      }
    });
  });

  describe('Payout Flow', () => {
    test('5. Setup club for payouts', async () => {
      const response = await request(app)
        .post('/api/club/payout/setup')
        .set('Authorization', `Bearer ${authToken}`);

      console.log('Payout Setup Response:', JSON.stringify(response.body, null, 2));

      if (response.status === 201) {
        expect(response.body.success).toBe(true);
        expect(response.body.data).toHaveProperty('contact_id');
        expect(response.body.data).toHaveProperty('fund_account_id');
      } else {
        // Might fail if bank details not verified
        expect(response.status).toBe(400);
      }
    });

    test('6. Calculate payout preview', async () => {
      const response = await request(app)
        .post(`/api/club/payout/calculate/${tournamentId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          platformFeePercentage: 5,
          processingFeePercentage: 2.5,
          gstPercentage: 18
        });

      console.log('Payout Calculation Response:', JSON.stringify(response.body, null, 2));

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('calculation');
      expect(response.body.data.calculation).toHaveProperty('total_collected');
      expect(response.body.data.calculation).toHaveProperty('platform_fee');
      expect(response.body.data.calculation).toHaveProperty('club_payout');
    });

    test('7. Create tournament payout', async () => {
      const response = await request(app)
        .post('/api/club/payout/create')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          tournament_id: tournamentId,
          urgent: false
        });

      console.log('Payout Creation Response:', JSON.stringify(response.body, null, 2));

      if (response.status === 201) {
        expect(response.body.success).toBe(true);
        expect(response.body.data).toHaveProperty('payout_id');
        expect(response.body.data).toHaveProperty('amount');
        expect(response.body.data).toHaveProperty('status');
        
        payoutId = response.body.data.payout_id;
      } else {
        // Might fail if setup not complete
        expect(response.status).toBe(400);
      }
    });

    test('8. Get payout status', async () => {
      if (!payoutId) {
        console.log('Skipping payout status test - no payout created');
        return;
      }

      const response = await request(app)
        .get(`/api/club/payout/status/${payoutId}`)
        .set('Authorization', `Bearer ${authToken}`);

      console.log('Payout Status Response:', JSON.stringify(response.body, null, 2));

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('payout_id');
      expect(response.body.data).toHaveProperty('status');
    });

    test('9. Get all club payouts', async () => {
      const response = await request(app)
        .get('/api/club/payout/list')
        .set('Authorization', `Bearer ${authToken}`)
        .query({ page: 1, limit: 10 });

      console.log('Club Payouts Response:', JSON.stringify(response.body, null, 2));

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('payouts');
      expect(response.body.data).toHaveProperty('pagination');
      expect(Array.isArray(response.body.data.payouts)).toBe(true);
    });
  });

  describe('Error Handling Tests', () => {
    test('10. Create duplicate banking details should fail', async () => {
      const response = await request(app)
        .post('/api/club/bank/create')
        .set('Authorization', `Bearer ${authToken}`)
        .send(testBankData);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('already exist');
    });

    test('11. Create payout for non-existent tournament should fail', async () => {
      const response = await request(app)
        .post('/api/club/payout/create')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          tournament_id: '********-0000-0000-0000-************',
          urgent: false
        });

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('not found');
    });

    test('12. Access without authentication should fail', async () => {
      const response = await request(app)
        .get('/api/club/bank/details');

      expect(response.status).toBe(401);
    });
  });
});
