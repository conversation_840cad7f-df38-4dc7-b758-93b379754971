const express = require("express");
const router = express.Router();
const {
  handleRazorPayWebhook,
  getWebhookEvents,
} = require("../controllers/webhookController");

// Middleware to parse raw body for webhook signature verification
const rawBodyParser = express.raw({ type: 'application/json' });

// RazorPay webhook endpoint (no auth required)
router.post("/razorpay", rawBodyParser, handleRazorPayWebhook);

// Debug endpoint to see webhook configuration (admin only)
router.get("/events", getWebhookEvents);

module.exports = router;
