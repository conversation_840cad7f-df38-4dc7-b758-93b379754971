
const { 
  setupClubForPayouts,
  createTournamentPayout,
  checkPayoutStatus,
  calculateTournamentPayout, // This function doesn't exist in your razorpay file - you'll need to create it
  // handlePayoutWebhook // This function doesn't exist in your razorpay file - you'll need to create it
} = require("../utils/razorPay");
const { sendResponse, handleError } = require("../utils/apiResponse");
const { Bankdetails, Tournament, Payout } = require("../config/db").models;

const { Op } = require('sequelize');

/**
 * Setup club for payouts (one-time setup)
 */
const setupClubPayouts = async (req, res) => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      return sendResponse(res, 403, {
        success: false,
        error: "Forbidden",
      });
    }

    // Get club's verified bank details
    const bankDetails = await Bankdetails.findOne({
      where: { 
        clubId: userId,
        isVerified: true,
        isLocked: true
      },
    });

    if (!bankDetails) {
      return sendResponse(res, 400, {
        success: false,
        error: "Verified bank details not found",
        message: "Please add and verify your bank details before setting up payouts",
      });
    }

    // Check if already setup
    const existingPayout = await Payout.findOne({
      where: { 
        clubId: userId,
        status: 'setup_completed'
      }
    });

    if (existingPayout) {
      return sendResponse(res, 200, {
        success: true,
        message: "Payout setup already completed",
        data: {
          contact_id: existingPayout.contact_id,
          fund_account_id: existingPayout.fund_account_id,
          setup_date: existingPayout.createdAt,
        },
      });
    }

    // Prepare club data for setup
    const clubData = {
      id: userId,
      name: req.user.name || `Club ${userId}`,
      email: req.user.email,
      phone: req.user.phone || "**********",
      bankDetails: {
        accountHolderName: bankDetails.bankAccountHolderName,
        accountNumber: bankDetails.AccountNumber,
        ifscCode: bankDetails.branchIFSCCode,
      },
    };

    console.log("🚀 Setting up club for payouts...");
    
    // Setup club for payouts
    const setupResult = await setupClubForPayouts(clubData);

    if (!setupResult.success) {
      return sendResponse(res, 400, {
        success: false,
        error: "Payout setup failed",
        details: setupResult.error,
      });
    }

    // Save payout setup details to database
    await Payout.create({
      clubId: userId,
      contact_id: setupResult.contact_id,
      fund_account_id: setupResult.fund_account_id,
      bank_details_id: bankDetails.id,
      status: 'setup_completed',
      verification_status: setupResult.verification.success ? 'verified' : 'pending',
      metadata: {
        setup_result: setupResult,
        bank_verification: setupResult.verification,
        setup_timestamp: new Date().toISOString(),
      },
    });

    return sendResponse(res, 201, {
      success: true,
      message: "Payout setup completed successfully",
      data: {
        contact_id: setupResult.contact_id,
        fund_account_id: setupResult.fund_account_id,
        verification_status: setupResult.verification.success ? 'verified' : 'pending',
        setup_date: new Date().toISOString(),
      },
    });

  } catch (error) {
    console.error("❌ Error in setupClubPayouts:", error);
    handleError(res, error);
  }
};

/**
 * Create tournament payout
 */
const createPayout = async (req, res) => {
  try {
    const userId = req.user?.userId;
    const { tournament_id, urgent = false, custom_amount = null } = req.body;

    if (!userId) {
      return sendResponse(res, 403, {
        success: false,
        error: "Forbidden",
      });
    }

    if (!tournament_id) {
      return sendResponse(res, 400, {
        success: false,
        error: "Tournament ID is required",
      });
    }

    // Get tournament details
    const tournament = await Tournament.findOne({
      where: { 
        id: tournament_id,
        clubId: userId,
        status: 'completed' // Only allow payouts for completed tournaments
      }
    });

    if (!tournament) {
      return sendResponse(res, 404, {
        success: false,
        error: "Tournament not found or not completed",
      });
    }

    // Check if payout already exists for this tournament
    const existingPayout = await Payout.findOne({
      where: { 
        tournament_id: tournament_id,
        status: { [Op.not]: 'failed' }
      }
    });

    if (existingPayout) {
      return sendResponse(res, 400, {
        success: false,
        error: "Payout already exists for this tournament",
        data: {
          payout_id: existingPayout.payout_id,
          status: existingPayout.status,
          amount: existingPayout.amount,
        },
      });
    }

    // Get club's payout setup
    const payoutSetup = await Payout.findOne({
      where: { 
        clubId: userId,
        status: 'setup_completed'
      }
    });

    if (!payoutSetup) {
      return sendResponse(res, 400, {
        success: false,
        error: "Payout setup not found",
        message: "Please complete payout setup before creating payouts",
      });
    }

    // Calculate payout amount based on tournament data
    const totalCollected = tournament.totalCollected || (tournament.participantCount * tournament.entryFee);
    
    // Calculate fees and final payout amount
    const platformFeePercentage = 5;
    const processingFeePercentage = 2.5;
    const gstPercentage = 18;
    
    const platformFee = (totalCollected * platformFeePercentage) / 100;
    const processingFee = (totalCollected * processingFeePercentage) / 100;
    const gstOnProcessingFee = (processingFee * gstPercentage) / 100;
    const totalFees = platformFee + processingFee + gstOnProcessingFee;
    const finalPayoutAmount = custom_amount || (totalCollected - totalFees);

    if (finalPayoutAmount <= 0) {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid payout amount",
        message: "Payout amount must be greater than 0",
      });
    }

    console.log("💰 Creating tournament payout...");

    // Create tournament payout
    const payoutResult = await createTournamentPayout({
      tournament_id: tournament.id,
      club_id: userId,
      fund_account_id: payoutSetup.fund_account_id,
      amount: finalPayoutAmount,
      tournament_name: tournament.name,
      urgent,
    });

    if (!payoutResult.success) {
      return sendResponse(res, 400, {
        success: false,
        error: "Payout creation failed",
        details: payoutResult.error,
      });
    }

    // Save payout record
    const payoutRecord = await Payout.create({
      clubId: userId,
      tournament_id: tournament.id,
      payout_id: payoutResult.payout_id,
      amount: finalPayoutAmount,
      total_collected: totalCollected,
      platform_fee: platformFee,
      processing_fee: processingFee + gstOnProcessingFee,
      status: payoutResult.status,
      mode: payoutResult.mode,
      reference_id: payoutResult.reference_id,
      fund_account_id: payoutSetup.fund_account_id,
      contact_id: payoutSetup.contact_id,
      urgent_transfer: urgent,
      metadata: {
        calculation: {
          totalCollected,
          platformFee,
          processingFee,
          gstOnProcessingFee,
          totalFees,
          clubPayout: finalPayoutAmount,
          clubPayoutPercentage: ((finalPayoutAmount / totalCollected) * 100).toFixed(2),
        },
        payout_details: {
          payout_id: payoutResult.payout_id,
          status: payoutResult.status,
          mode: payoutResult.mode,
          created_at: payoutResult.created_at,
        },
        tournament_data: {
          id: tournament.id,
          name: tournament.name,
          participantCount: tournament.participantCount,
          entryFee: tournament.entryFee,
        },
        created_timestamp: new Date().toISOString(),
      },
    });

    return sendResponse(res, 201, {
      success: true,
      message: "Tournament payout created successfully",
      data: {
        payout_id: payoutResult.payout_id,
        amount: finalPayoutAmount,
        status: payoutResult.status,
        mode: payoutResult.mode,
        estimated_processing_time: payoutResult.estimated_processing_time,
        reference_id: payoutResult.reference_id,
        calculation: {
          total_collected: totalCollected,
          platform_fee: platformFee,
          processing_fee: processingFee + gstOnProcessingFee,
          club_payout: finalPayoutAmount,
          payout_percentage: ((finalPayoutAmount / totalCollected) * 100).toFixed(2),
        },
        tournament: {
          id: tournament.id,
          name: tournament.name,
          participants: tournament.participantCount,
        },
      },
    });

  } catch (error) {
    console.error("❌ Error in createPayout:", error);
    handleError(res, error);
  }
};

/**
 * Get payout status
 */
const getPayoutStatus = async (req, res) => {
  try {
    const userId = req.user?.userId;
    const { payout_id } = req.params;

    if (!userId) {
      return sendResponse(res, 403, {
        success: false,
        error: "Forbidden",
      });
    }

    if (!payout_id) {
      return sendResponse(res, 400, {
        success: false,
        error: "Payout ID is required",
      });
    }

    // Get payout record from database
    const payoutRecord = await Payout.findOne({
      where: { 
        payout_id: payout_id,
        clubId: userId 
      },
      include: [
        {
          model: Tournament,
          attributes: ['id', 'name', 'status'],
        }
      ]
    });

    if (!payoutRecord) {
      return sendResponse(res, 404, {
        success: false,
        error: "Payout not found",
      });
    }

    console.log("🔍 Checking payout status from Razorpay...");

    // Get latest status from Razorpay
    const statusResult = await checkPayoutStatus(payout_id);

    if (statusResult.success) {
      // Update local database with latest status
      await payoutRecord.update({
        status: statusResult.payout.status,
        processed_at: statusResult.payout.processed_at,
        failure_reason: statusResult.payout.failure_reason,
        metadata: {
          ...payoutRecord.metadata,
          last_status_check: new Date().toISOString(),
          razorpay_status: statusResult.payout,
        },
      });
    }

    return sendResponse(res, 200, {
      success: true,
      data: {
        payout_id: payoutRecord.payout_id,
        status: statusResult.success ? statusResult.payout.status : payoutRecord.status,
        status_description: statusResult.success ? statusResult.payout.status_description : "Status check failed",
        amount: payoutRecord.amount,
        mode: payoutRecord.mode,
        reference_id: payoutRecord.reference_id,
        created_at: payoutRecord.createdAt,
        processed_at: statusResult.success ? statusResult.payout.processed_at : payoutRecord.processed_at,
        failure_reason: statusResult.success ? statusResult.payout.failure_reason : payoutRecord.failure_reason,
        utr: statusResult.success ? statusResult.payout.utr : null,
        fees: statusResult.success ? statusResult.payout.fees : null,
        tax: statusResult.success ? statusResult.payout.tax : null,
        tournament: payoutRecord.Tournament ? {
          id: payoutRecord.Tournament.id,
          name: payoutRecord.Tournament.name,
          status: payoutRecord.Tournament.status,
        } : null,
      },
    });

  } catch (error) {
    console.error("❌ Error in getPayoutStatus:", error);
    handleError(res, error);
  }
};

/**
 * Get all payouts for a club
 */
const getClubPayouts = async (req, res) => {
  try {
    const userId = req.user?.userId;
    const { page = 1, limit = 10, status = null } = req.query;

    if (!userId) {
      return sendResponse(res, 403, {
        success: false,
        error: "Forbidden",
      });
    }

    const offset = (page - 1) * limit;
    const whereClause = { clubId: userId };
    
    if (status) {
      whereClause.status = status;
    }

    const { rows: payouts, count: total } = await Payout.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Tournament,
          attributes: ['id', 'name', 'status', 'participantCount'],
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']],
    });

    const formattedPayouts = payouts.map(payout => ({
      id: payout.id,
      payout_id: payout.payout_id,
      amount: payout.amount,
      status: payout.status,
      mode: payout.mode,
      reference_id: payout.reference_id,
      urgent_transfer: payout.urgent_transfer,
      created_at: payout.createdAt,
      processed_at: payout.processed_at,
      tournament: payout.Tournament ? {
        id: payout.Tournament.id,
        name: payout.Tournament.name,
        status: payout.Tournament.status,
        participants: payout.Tournament.participantCount,
      } : null,
    }));

    return sendResponse(res, 200, {
      success: true,
      data: {
        payouts: formattedPayouts,
        pagination: {
          current_page: parseInt(page),
          per_page: parseInt(limit),
          total: total,
          total_pages: Math.ceil(total / limit),
        },
      },
    });

  } catch (error) {
    console.error("❌ Error in getClubPayouts:", error);
    handleError(res, error);
  }
};

/**
 * Calculate payout for a tournament (preview)
 */
const calculatePayoutPreview = async (req, res) => {
  try {
    const userId = req.user?.userId;
    const { tournament_id } = req.params;
    const { 
      platformFeePercentage = 5,
      processingFeePercentage = 2.5,
      gstPercentage = 18,
      customDeductions = 0 
    } = req.body;

    if (!userId) {
      return sendResponse(res, 403, {
        success: false,
        error: "Forbidden",
      });
    }

    if (!tournament_id) {
      return sendResponse(res, 400, {
        success: false,
        error: "Tournament ID is required",
      });
    }

    // Get tournament details
    const tournament = await Tournament.findOne({
      where: { 
        id: tournament_id,
        clubId: userId 
      }
    });

    if (!tournament) {
      return sendResponse(res, 404, {
        success: false,
        error: "Tournament not found",
      });
    }

    // Calculate payout manually since calculateTournamentPayout doesn't exist in your razorpay file
    const totalCollected = tournament.totalCollected || (tournament.participantCount * tournament.entryFee);
    const platformFee = (totalCollected * platformFeePercentage) / 100;
    const processingFee = (totalCollected * processingFeePercentage) / 100;
    const gstOnProcessingFee = (processingFee * gstPercentage) / 100;
    const totalDeductions = platformFee + processingFee + gstOnProcessingFee + customDeductions;
    const clubPayout = totalCollected - totalDeductions;
    const clubPayoutPercentage = ((clubPayout / totalCollected) * 100).toFixed(2);

    const calculation = {
      totalCollected,
      platformFee,
      processingFee,
      gstOnProcessingFee,
      customDeductions,
      totalDeductions,
      clubPayout,
      clubPayoutPercentage,
      breakdown: {
        "Total Collected": `₹${totalCollected.toFixed(2)}`,
        "Platform Fee (${platformFeePercentage}%)": `₹${platformFee.toFixed(2)}`,
        "Processing Fee (${processingFeePercentage}%)": `₹${processingFee.toFixed(2)}`,
        "GST on Processing Fee (${gstPercentage}%)": `₹${gstOnProcessingFee.toFixed(2)}`,
        "Custom Deductions": `₹${customDeductions.toFixed(2)}`,
        "Total Deductions": `₹${totalDeductions.toFixed(2)}`,
        "Club Payout": `₹${clubPayout.toFixed(2)}`,
      }
    };

    return sendResponse(res, 200, {
      success: true,
      data: {
        tournament: {
          id: tournament.id,
          name: tournament.name,
          participants: tournament.participantCount,
          entry_fee: tournament.entryFee,
        },
        calculation: {
          total_collected: calculation.totalCollected,
          platform_fee: calculation.platformFee,
          processing_fee: calculation.processingFee,
          custom_deductions: calculation.customDeductions,
          total_deductions: calculation.totalDeductions,
          club_payout: calculation.clubPayout,
          payout_percentage: calculation.clubPayoutPercentage,
        },
        breakdown: calculation.breakdown,
      },
    });

  } catch (error) {
    console.error("❌ Error in calculatePayoutPreview:", error);
    handleError(res, error);
  }
};

/**
 * Handle Razorpay webhook for payout updates
 * Note: You'll need to implement handlePayoutWebhook in your razorpay utils file
 */
const handleWebhook = async (req, res) => {
  try {
    const payload = JSON.stringify(req.body);
    const signature = req.headers['x-razorpay-signature'];

    console.log("📬 Received Razorpay webhook");

    // Basic webhook validation and processing
    // You'll need to implement this function in your razorpay utils
    if (!signature) {
      return sendResponse(res, 400, {
        success: false,
        error: "Missing webhook signature",
      });
    }

    const event = req.body;

    if (event.event && event.event.includes('payout')) {
      // Process payout webhook
      const payoutData = event.payload?.payout?.entity;
      
      if (payoutData && payoutData.id) {
        // Update payout status in database
        const payoutRecord = await Payout.findOne({
          where: { payout_id: payoutData.id }
        });

        if (payoutRecord) {
          await payoutRecord.update({
            status: payoutData.status,
            processed_at: payoutData.processed_at,
            failure_reason: payoutData.failure_reason,
            metadata: {
              ...payoutRecord.metadata,
              webhook_update: new Date().toISOString(),
              razorpay_webhook_data: payoutData,
            },
          });
        }
      }
    }

    return sendResponse(res, 200, {
      success: true,
      message: "Webhook processed successfully",
      event: event.event,
    });

  } catch (error) {
    console.error("❌ Error in handleWebhook:", error);
    return sendResponse(res, 500, {
      success: false,
      error: "Internal server error",
    });
  }
};

module.exports = {
  setupClubPayouts,
  createPayout,
  getPayoutStatus,
  getClubPayouts,
  calculatePayoutPreview,
  handleWebhook,
};
