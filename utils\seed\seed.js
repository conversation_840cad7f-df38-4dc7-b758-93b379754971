const bcrypt = require("bcryptjs");
const { v4: uuidv4 } = require("uuid");
// const tournament = require("../models/tournament");
const generateUsers = require("./user");

/**
 * Seed the database with sample data
 * @param {Object} models - The models object from the database configuration
 * @param {Object} options - Options for seeding
 * @param {boolean} options.ignoreDuplicates - Whether to ignore duplicate entries
 * @returns {Promise<void>}
 */
const seedDatabase = async (models, options = {}) => {
    try {
        console.log("Starting database seeding...");

        // Create sample users
        const users = await createUsers(models.User, options);
        console.log("✅ Users created successfully");

        if (!users || !users.clubUsers || users.clubUsers.length === 0) {
            console.log(
                "⚠️ No club users were created. Skipping related data creation."
            );
            return;
        }

        // Create sample club details
        await createClubDetails(models.ClubDetail, users.clubUsers, options);
        console.log("✅ Club details created successfully");

        if (!users.playerUsers || users.playerUsers.length === 0) {
            console.log(
                "⚠️ No player users were created. Skipping player details creation."
            );
        } else {
            // Create sample player details
            await createPlayerDetails(
                models.PlayerDetail,
                users.playerUsers,
                options
            );
            console.log("✅ Player details created successfully");
        }

        // Create sample tournaments
        await createTournaments(models.Tournament, users.clubUsers, options);
        console.log("✅ Tournaments created successfully");

        console.log("Database seeding completed successfully!");

        if (!users.arbiterUsers || users.arbiterUsers.length === 0) {
            console.log("user-details", users.arbiterUsers, users.arbiterUsers);

            console.log(
                "⚠️ No arbiter users were created. Skipping arbiter details creation."
            );
        } else {
            // Create sample arbiter details
            await createArbiterDetails(
                models.ArbiterDetails,
                users.arbiterUsers,
                options
            );
            console.log("✅ Arbiter details created successfully");
        }
    } catch (error) {
        console.error("Error seeding database:", error);
        throw error;
    }
};

/**
 * Create sample users
 * @param {Object} User - The User model
 * @param {Object} options - Options for seeding
 * @returns {Promise<Object>} - Object containing arrays of created users by role
 */
const createUsers = async (User, options = {}) => {
    // Hash password once for all users
    const hashedPassword = await bcrypt.hash("password123", 10);

    // Check for existing users to avoid duplicates
    const existingUsers = await User.findAll({
        attributes: ["email", "name", "cbid"],
    });

    // Create a map of existing users for quick lookup
    const existingEmails = new Set(existingUsers.map((user) => user.email));
    const existingNames = new Set(existingUsers.map((user) => user.name));
    const existingCbids = new Set(existingUsers.map((user) => user.cbid));

    // Sample user data
    //   const userData = [
    //     // Admin users
    //     {
    //       name: "Admin User",
    //       cbid: "ADMIN001",
    //       email: "<EMAIL>",
    //       phoneNumber: "98********",
    //       password: hashedPassword,
    //       role: "admin",
    //     },
    //     // Arbiter users
    //     {
    //       name: "Arbiter User1",
    //       cbid: "001",
    //       email: "<EMAIL>",
    //       phoneNumber: "9873543210",
    //       password: hashedPassword,
    //       role: "arbiter",
    //     },
    //     {
    //       name: "Arbiter User2",
    //       cbid: "002",
    //       email: "<EMAIL>",
    //       phoneNumber: "9873543211",
    //       password: hashedPassword,
    //       role: "arbiter",
    //     },
    //     {
    //       name: "Arbiter User3",
    //       cbid: "003",
    //       email: "<EMAIL>",
    //       phoneNumber: "9873543212",
    //       password: hashedPassword,
    //       role: "arbiter",
    //     },
    //     {
    //       name: "Arbiter User4",
    //       cbid: "004",
    //       email: "<EMAIL>",
    //       phoneNumber: "9873543213",
    //       password: hashedPassword,
    //       role: "arbiter",
    //     },
    //     {
    //       name: "Arbiter User5",
    //       cbid: "005",
    //       email: "<EMAIL>",
    //       phoneNumber: "9873543214",
    //       password: hashedPassword,
    //       role: "arbiter",
    //     },
    //     {
    //       name: "Arbiter User6",
    //       cbid: "006",
    //       email: "<EMAIL>",
    //       phoneNumber: "9873543215",
    //       password: hashedPassword,
    //       role: "arbiter",
    //     },
    //     // Club users
    //     {
    //       name: "Chennai Chess Club",
    //       cbid: "CLUB001",
    //       email: "<EMAIL>",
    //       phoneNumber: "9876543211",
    //       password: hashedPassword,
    //       role: "club",
    //     },
    //     {
    //       name: "Delhi Chess Academy",
    //       cbid: "CLUB002",
    //       email: "<EMAIL>",
    //       phoneNumber: "9876543212",
    //       password: hashedPassword,
    //       role: "club",
    //     },
    //     {
    //       name: "Mumbai Chess Association",
    //       cbid: "CLUB003",
    //       email: "<EMAIL>",
    //       phoneNumber: "9876543213",
    //       password: hashedPassword,
    //       role: "club",
    //     },
    //     // Player users
    //     {
    //       name: "Viswanathan Anand",
    //       cbid: "CB25US00003",
    //       email: "<EMAIL>",
    //       phoneNumber: "9876543214",
    //       password: hashedPassword,
    //       role: "player",
    //     },
    //     {
    //       name: "Koneru Humpy",
    //       cbid: "CB25US00004",
    //       email: "<EMAIL>",
    //       phoneNumber: "9876543215",
    //       password: hashedPassword,
    //       role: "player",
    //     },
    //     {
    //       name: "Pentala Harikrishna",
    //       cbid: "CB25US00005",
    //       email: "<EMAIL>",
    //       phoneNumber: "9876543216",
    //       password: hashedPassword,
    //       role: "player",
    //     },
    //     {
    //       name: "R Praggnanandhaa",
    //       cbid: "CB25US00006",
    //       email: "<EMAIL>",
    //       phoneNumber: "9876543217",
    //       password: hashedPassword,
    //       role: "player",
    //     },
    //     {
    //       name: "D Gukesh",
    //       cbid: "CB25US00007",
    //       email: "<EMAIL>",
    //       phoneNumber: "9876543218",
    //       password: hashedPassword,
    //       role: "player",
    //     },
    //     {
    //       name: "Nihal Sarin",
    //       cbid: "CB25US00008",
    //       email: "<EMAIL>",
    //       phoneNumber: "9876543219",
    //       password: hashedPassword,
    //       role: "player",
    //     },
    //     {
    //       name: "Arjun Erigaisi",
    //       cbid: "CB25US00009",
    //       email: "<EMAIL>",
    //       phoneNumber: "9876543220",
    //       password: hashedPassword,
    //       role: "player",
    //     },
    //     {
    //       name: "Harika Dronavalli",
    //       cbid: "CB25US00010",
    //       email: "<EMAIL>",
    //       phoneNumber: "9876543221",
    //       password: hashedPassword,
    //       role: "player",
    //     },
    //     {
    //       name: "Tania Sachdev",
    //       cbid: "CB25US00011",
    //       email: "<EMAIL>",
    //       phoneNumber: "9876543222",
    //       password: hashedPassword,
    //       role: "player",
    //     },
    //     {
    //       name: "Vidit Gujrathi",
    //       cbid: "CB25US00012",
    //       email: "<EMAIL>",
    //       phoneNumber: "9876543223",
    //       password: hashedPassword,
    //       role: "player",
    //     },
    //     {
    //       name: "B Adhiban",
    //       cbid: "CB25US00013",
    //       email: "<EMAIL>",
    //       phoneNumber: "9876543224",
    //       password: hashedPassword,
    //       role: "player",
    //     },
    //     {
    //       name: "S P Sethuraman",
    //       cbid: "CB25US00014",
    //       email: "<EMAIL>",
    //       phoneNumber: "9876543225",
    //       password: hashedPassword,
    //       role: "player",
    //     },
    //     {
    //       name: "Surya Shekhar Ganguly",
    //       cbid: "CB25US00015",
    //       email: "<EMAIL>",
    //       phoneNumber: "9876543226",
    //       password: hashedPassword,
    //       role: "player",
    //     },
    //     {
    //       name: "Aravindh Chithambaram",
    //       cbid: "CB25US00016",
    //       email: "<EMAIL>",
    //       phoneNumber: "9876543227",
    //       password: hashedPassword,
    //       role: "player",
    //     },
    //     {
    //       name: "Sasikiran Krishnan",
    //       cbid: "CB25US00017",
    //       email: "<EMAIL>",
    //       phoneNumber: "9876543228",
    //       password: hashedPassword,
    //       role: "player",
    //     },
    //     {
    //       name: "Vaishali Rameshbabu",
    //       cbid: "CB25US00018",
    //       email: "<EMAIL>",
    //       phoneNumber: "9876543229",
    //       password: hashedPassword,
    //       role: "player",
    //     },
    //     {
    //       name: "Divya Deshmukh",
    //       cbid: "CB25US00019",
    //       email: "<EMAIL>",
    //       phoneNumber: "9876543230",
    //       password: hashedPassword,
    //       role: "player",
    //     },
    //     {
    //       name: "Abhijeet Gupta",
    //       cbid: "CB25US00020",
    //       email: "<EMAIL>",
    //       phoneNumber: "9876543231",
    //       password: hashedPassword,
    //       role: "player",
    //     },
    //   ];

    const userData = await generateUsers();
    // Filter out users that already exist
    const filteredUserData = userData.filter((user) => {
        const exists =
            existingEmails.has(user.email) ||
            existingNames.has(user.name) ||
            existingCbids.has(user.cbid);
        if (exists) {
            console.log(`Skipping existing user: ${user.name} (${user.email})`);
        }
        return !exists;
    });

    let createdUsers = [];

    if (filteredUserData.length > 0) {
        // Create new users with ignoreDuplicates option
        createdUsers = await User.bulkCreate(filteredUserData, {
            ignoreDuplicates: options.ignoreDuplicates || true,
        });
        console.log(`Created ${createdUsers.length} new users`);
    } else {
        console.log("No new users to create");
    }

    // Get all users including existing ones for the relationships
    const allUsers = await User.findAll();

    // Separate users by role for easier access
    const adminUsers = allUsers.filter((user) => user.role === "admin");
    const clubUsers = allUsers.filter((user) => user.role === "club");
    const playerUsers = allUsers.filter((user) => user.role === "player");
    const arbiterUsers = allUsers.filter((user) => user.role === "arbiter");

    return { adminUsers, clubUsers, playerUsers, allUsers, arbiterUsers };
};

/**
 * Create sample club details
 * @param {Object} ClubDetail - The ClubDetail model
 * @param {Array} clubUsers - Array of club users (must include club name and id)
 * @param {Object} options - Options for seeding
 * @returns {Promise<Array>} - Array of created club details
 */
const createClubDetails = async (ClubDetail, clubUsers, options = {}) => {
    const existingClubDetails = await ClubDetail.findAll({
        attributes: ["userId"],
    });

    const existingUserIds = new Set(existingClubDetails.map((club) => club.userId));

    const filteredClubUsers = clubUsers.filter((user) => {
        const exists = existingUserIds.has(user.id);
        if (exists) {
            console.log(`Skipping existing club detail for user: ${user.name}`);
        }
        return !exists;
    });

    if (filteredClubUsers.length === 0) {
        console.log("No new club details to create");
        return [];
    }

    const sanitizeForSlug = (name) =>
        name
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, "-")
            .replace(/^-+|-+$/g, "");

    const extractCity = (clubName) => {
        const match = clubName.match(/^([A-Za-z]+)[\s-]/);
        return match ? match[1] : "City";
    };

    const defaultStateMap = {
        Chennai: "Tamil Nadu",
        Delhi: "Delhi",
        Mumbai: "Maharashtra",
        Hyderabad: "Telangana",
        Bangalore: "Karnataka",
        Kolkata: "West Bengal",
        Pune: "Maharashtra",
        Ahmedabad: "Gujarat",
        Jaipur: "Rajasthan",
        Lucknow: "Uttar Pradesh",
    };

    const clubDetailsData = filteredClubUsers.map((clubUser, index) => {
        const clubName = clubUser.name;
        const city = extractCity(clubName);
        const state = defaultStateMap[city] || "Unknown";
        const district = city;
        const clubSlug = sanitizeForSlug(clubName);

        return {
            clubName,
            clubId: clubSlug,
            clubDistrictId: `${city.toUpperCase().slice(0, 3)}${index.toString().padStart(3, "0")}`,
            userId: clubUser.id,
            tournamentStatus: "inactive",
            contactPersonName: `Contact Person ${index + 1}`,
            contactPersonNumber: `98765432${(index + 10).toString().padStart(2, "0")}`,
            contactPersonEmail: `contact${index + 1}@chessbrigade.com`,
            alternateContactNumber: `87654321${(index + 10).toString().padStart(2, "0")}`,
            country: "India",
            state,
            district,
            city,
            pincode: `6000${(index + 1).toString().padStart(2, "0")}`,
            address: `123 Chess Street, ${city}, ${state}`,
            locationUrl: `https://maps.google.com/maps?q=${encodeURIComponent(city + " " + state)}`,
            authorizedSignatoryName: `Signatory ${index + 1}`,
            authorizedSignatoryContactNumber: `********${(index + 10).toString().padStart(2, "0")}`,
            authorizedSignatoryEmail: `signatory${index + 1}@chessbrigade.com`,
            authorizedSignatoryDesignation: "President",
            bankName: "State Bank of India",
            AccountNumber: `**********${index}`,
            branchIFSCCode: `SBIN0001${index}`,
            branchName: `SBI ${city} Branch`,
            spotEntry: false,
            bankAccountType: "Current",
            bankAccountHolderName: clubName,
            countryCode: "IN",
            stateCode: state.slice(0, 2).toUpperCase(),
            profileUrl: `https://example.com/clubs/${index + 1}.jpg`,
        };
    });

    return await ClubDetail.bulkCreate(clubDetailsData, {
        ignoreDuplicates: options.ignoreDuplicates ?? true,
    });
};


/**
 * Create sample player details
 * @param {Object} PlayerDetail - The PlayerDetail model
 * @param {Array} playerUsers - Array of player users
 * @param {Object} options - Options for seeding
 * @returns {Promise<Array>} - Array of created player details
 */
const createPlayerDetails = async (PlayerDetail, playerUsers, options = {}) => {
    const existingPlayerDetails = await PlayerDetail.findAll({ attributes: ["userId"] });
    const existingUserIds = new Set(existingPlayerDetails.map((player) => player.userId));

    const filteredPlayerUsers = playerUsers.filter((user) => {
        const exists = existingUserIds.has(user.id);
        if (exists) console.log(`Skipping existing player detail for user: ${user.name}`);
        return !exists;
    });

    if (filteredPlayerUsers.length === 0) {
        console.log("No new player details to create");
        return [];
    }

    const { ClubDetail } = require("../../config/db").models;
    const clubDetails = await ClubDetail.findAll();
    const clubNameToIdMap = {};
    clubDetails.forEach((club) => {
        clubNameToIdMap[club.clubName] = club.id;
    });

    console.log("Club details fetched for player assignments:", Object.keys(clubNameToIdMap));

    const playerDetailsData = filteredPlayerUsers.map((playerUser, index) => {
        const randomClubName = Object.keys(clubNameToIdMap)[index % Object.keys(clubNameToIdMap).length];
        const genders = ["male", "female"];
        const randomGender = genders[Math.floor(Math.random() * genders.length)];
        const randomDate = new Date(1990 + Math.floor(Math.random() * 15), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1);

        const locationData = [
            {
                state: "Tamil Nadu",
                districts: [
                    { district: "Chennai", cities: ["Chennai"], pincodes: ["600001", "600002", "600003"] },
                    { district: "Coimbatore", cities: ["Coimbatore"], pincodes: ["641001", "641002"] },
                ],
            },
            {
                state: "Karnataka",
                districts: [
                    { district: "Bengaluru Urban", cities: ["Bengaluru"], pincodes: ["560001", "560002"] },
                    { district: "Mysuru", cities: ["Mysuru"], pincodes: ["570001", "570002"] },
                ],
            },
            {
                state: "Maharashtra",
                districts: [
                    { district: "Mumbai Suburban", cities: ["Mumbai"], pincodes: ["400001", "400002"] },
                    { district: "Pune", cities: ["Pune"], pincodes: ["411001", "411002"] },
                ],
            },
            {
                state: "Delhi",
                districts: [
                    { district: "New Delhi", cities: ["Delhi"], pincodes: ["110001", "110002"] },
                ],
            },
            // Add more states as needed
        ];

        // Inside your map function, generate location info randomly:
        const loc = locationData[Math.floor(Math.random() * locationData.length)];
        const distObj = loc.districts[Math.floor(Math.random() * loc.districts.length)];
        const city = distObj.cities[Math.floor(Math.random() * distObj.cities.length)];
        const pincode = distObj.pincodes[Math.floor(Math.random() * distObj.pincodes.length)];

        return {
            playerTitle: index % 4 === 0 ? "IM" : "GM",
            playerName: playerUser.name,
            profileUrl: `https://example.com/players/${index + 1}.jpg`,
            userId: playerUser.id,
            dob: randomDate.toISOString().split("T")[0],
            gender: randomGender,
            parentGuardianName: "Parent Name",
            emergencyContact: `98765432${(100 + index) % 1000}`,
            alternateContact: `87654321${(100 + index) % 1000}`,
            fideRating: `${2300 + Math.floor(Math.random() * 500)}`,
            fideId: `${5000000 + index}`,
            aicfId: `AICF${String(index + 1).padStart(3, "0")}`,
            stateId: `STATE${index + 1}`,
            districtId: `DIST${index + 1}`,
            association: "All India Chess Federation",
            club: randomClubName,
            clubId: clubNameToIdMap[randomClubName],
            country: "India",
            state: loc.state,
            district: distObj.district,
            city: city,
            pincode: pincode,
            address: `123 Chess Street, ${city}, ${loc.state}`,
            termsAndConditions: true,
        };
    });

    return await PlayerDetail.bulkCreate(playerDetailsData, {
        ignoreDuplicates: options.ignoreDuplicates || true,
    });
};

module.exports = createPlayerDetails;


/**
 * Create sample tournaments
 * @param {Object} Tournament - The Tournament model
 * @param {Array} clubUsers - Array of club users
 * @param {Object} options - Options for seeding
 * @returns {Promise<Array>} - Array of created tournaments
 */
const createTournaments = async (Tournament, clubUsers, options = {}) => {
    // Check for existing tournaments to avoid duplicates
    const existingTournaments = await Tournament.findAll({
        attributes: ["title"],
    });

    // Create a set of existing tournament titles for quick lookup
    const existingTitles = new Set(
        existingTournaments.map((tournament) => tournament.title)
    );

    const tournamentData = [];

    // Create 2 tournaments for each club
    clubUsers.forEach((clubUser) => {
        const levels = ["national", "state", "district", "global"];``
        const systems = ["swiss-system", "round-robin", "knockout"];

        const {
        startDate,
        endDate,
        registrationStartDate,
        registrationEndDate,
    } = generateTournamentDates();

        for (let i = 0; i < 2; i++) {
            const tournamentTitle = `${clubUser.name
                .toLowerCase()
                .replace(/\s+/g, "-")}-championship-${i + 1}`;
            const reportingTime = "09:00 AM";

            // Skip if tournament with this title already exists
            if (existingTitles.has(tournamentTitle)) {
                console.log(`Skipping existing tournament: ${tournamentTitle}`);
                continue;
            }

 const Category = ["open", "male", "female"][
  Math.floor(Math.random() * 3)
];

            tournamentData.push({
                clubId: clubUser.id,
                title: tournamentTitle,
                subTitle:`sub-${tournamentTitle}`,
                presentedBy:`Presented by ${tournamentTitle}`,
                fideRated: i === 0, // First tournament is FIDE rated
                organizerName: clubUser.name,
                tournamentLevel: levels[Math.floor(Math.random() * levels.length)],
                startDate:startDate,
                endDate:endDate,
                registrationStartDate:registrationStartDate,
                registrationEndDate:registrationEndDate,
                registrationEndTime: "11:59 PM",
                arbiterId: null,
                tournamentDirectorName: "Tournament Director",
                entryFeeCurrency: "INR",
                entryFee: 500 * (i + 1),
                // numberOfRounds: 5 + i,
                timeControl: "classical",
                reportingTime: reportingTime,
                timeControlDuration: "90",
                timeControlIncrement: "30",
                tournamentType: "team",
                tournamentSystem: systems[Math.floor(Math.random() * systems.length)],
                nationalApproval: "Approved",
                stateApproval: "Approved",
                districtApproval: "Approved",
                contactPersonName: `Contact Person ${i + 1}`,
                email: `tournament${i + 1}@${clubUser.email.split("@")[1]}`,
                contactNumber: clubUser.phoneNumber,
                alternateContactNumber: `8********${i}`,
                numberOfTrophiesMale: 3,
                numberOfTrophiesFemale: 3,
                totalCashPrizeCurrency: "INR",
                totalCashPrizeAmount: 10000,
                country: "India",
                state: clubUser.name.includes("Chennai")
                    ? "Tamil Nadu"
                    : clubUser.name.includes("Delhi")
                        ? "Delhi"
                        : "Maharashtra",
                district: clubUser.name.includes("Chennai")
                    ? "Chennai"
                    : clubUser.name.includes("Delhi")
                        ? "New Delhi"
                        : "Mumbai",
                city: clubUser.name.includes("Chennai")
                    ? "Chennai"
                    : clubUser.name.includes("Delhi")
                        ? "Delhi"
                        : "Mumbai",
                pincode: clubUser.name.includes("Chennai")
                    ? "600001"
                    : clubUser.name.includes("Delhi")
                        ? "110001"
                        : "400001",
                venueAddress: `123 Chess Street, ${clubUser.name.includes("Chennai")
                        ? "Chennai"
                        : clubUser.name.includes("Delhi")
                            ? "Delhi"
                            : "Mumbai"
                    }`,
                nearestLandmark: "Chess Park",
                brochureUrl: `https://example.com/tournaments/${i + 1}.pdf`,
                locationUrl: `https://maps.google.com/maps?q=chennai+tamil+nadu`,
                chessboardProvided: Math.random() < 0.5,
                timerProvided: Math.random() < 0.5,
                parkingFacility: ["yes", "no", "limited"][
                    Math.floor(Math.random() * 3)
                ],
                spotEntry: Math.random() < 0.3,
                tournamentStatus: "inactive",
                foodFacility: [
                    "breakfast",
                    "lunch",
                    "dinner",
                    "snacks",
                    "beverages",
                    "nil",
                ]
                    .sort(() => 0.5 - Math.random())
                    .slice(0, Math.floor(Math.random() * 6) + 1)
                    .join(","),
                  tournamentCategory:Category,   
                maleAgeCategory:Category ==='male'||Category=== 'open'? ["OPEN", "U18", "U15", "U12"]
                    .sort(() => 0.5 - Math.random())
                    .slice(0, Math.floor(Math.random() * 4) + 1):null,
                femaleAgeCategory:Category ==='female'||Category=== 'open'? ["OPEN", "U18", "U15", "U12"]
                    .sort(() => 0.5 - Math.random())
                    .slice(0, Math.floor(Math.random() * 4) + 1):null,               
            });
        }
    });

    if (tournamentData.length === 0) {
        console.log("No new tournaments to create");
        return [];
    }

    return await Tournament.bulkCreate(tournamentData, {
        ignoreDuplicates: options.ignoreDuplicates || true,
    });
};

/**
 * Create sample arbiter details
 * @param {Object} ArbiterDetails - The ArbiterDetails model
 * @param {Array} arbiterUsers - Array of arbiter users (with role = "arbiter")
 * @param {Object} options - Options for seeding
 * @returns {Promise<Array>} - Array of created arbiter details
 */
const createArbiterDetails = async (
    ArbiterDetails,
    arbiterUsers,
    options = {}
) => {
    // Fetch existing arbiter details to avoid duplication
    const existingArbiters = await ArbiterDetails.findAll({
        attributes: ["userId"],
    });

    const existingUserIds = new Set(existingArbiters.map((a) => a.userId));

    // Filter out users who already have arbiter details
    const filteredUsers = arbiterUsers.filter((user) => {
        const exists = existingUserIds.has(user.id);
        if (exists) {
            console.log(`Skipping existing arbiter detail for user: ${user.name}`);
        }
        return !exists;
    });

    if (filteredUsers.length === 0) {
        console.log("No new arbiter details to create");
        return [];
    }

    const titles = ["GM", "IM", "FM", "WGM", "WIM", "WFM"];
    const countries = ["India", "India", "India"];
    const states = ["Tamil Nadu", "Delhi", "Maharashtra"];
    const districts = ["Chennai", "New Delhi", "Mumbai"];
    const cities = ["Chennai", "Delhi", "Mumbai"];

    const arbiterDetailsData = filteredUsers.map((user, index) => ({
        userId: user.id,
        profileUrl: `https://example.com/arbiters/${index + 1}.jpg`,
        officialId: `ARB${1000 + index}`,
        title: titles[index % titles.length],
        phoneNumber: `987654321${index}`,
        email: `arbiter${index + 1}@chessbrigade.com`,
        alternateContactNumber: `8********${index}`,
        country: countries[index % countries.length],
        state: states[index % states.length],
        district: districts[index % districts.length],
        city: cities[index % cities.length],
        pincode: `6000${index + 1}`,
    }));

    return await ArbiterDetails.bulkCreate(arbiterDetailsData, {
        ignoreDuplicates: options.ignoreDuplicates || true,
    });
};

const generateTournamentDates = () => {
    const today = new Date();

    // Decide this or next month
    const monthOffset = Math.random() < 0.5 ? 0 : 1;
    const year = today.getFullYear();
    const month = today.getMonth() + monthOffset;

    // Start date: day 5 to 25 to ensure room for registration
    const startDay = Math.floor(Math.random() * 20) + 5;
    const startDate = new Date(year, month, startDay);

    // End date: 1-3 days after start
    const endDate = new Date(startDate);
    endDate.setDate(startDate.getDate() + Math.floor(Math.random() * 3) + 1);

    // Registration end: 1-3 days before start
    const registrationEndDate = new Date(startDate);
    registrationEndDate.setDate(startDate.getDate() - (Math.floor(Math.random() * 3) + 1));

    // Registration start: 2-6 days before registration end
    const registrationStartDate = new Date(registrationEndDate);
    registrationStartDate.setDate(registrationEndDate.getDate() - (Math.floor(Math.random() * 5) + 2));

    const format = (d) => d.toISOString().split("T")[0];

    return {
        startDate: format(startDate),
        endDate: format(endDate),
        registrationStartDate: format(registrationStartDate),
        registrationEndDate: format(registrationEndDate),
    };
};
 
module.exports = seedDatabase;