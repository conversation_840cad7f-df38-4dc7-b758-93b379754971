const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3000/api'; // Adjust to your server URL
const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'testpassword123';

// Test data
const testClubData = {
  name: 'Test Chess Club',
  email: TEST_EMAIL,
  phone: '**********',
  address: 'Test Address, Test City',
  description: 'Test club for integration testing'
};

const testBankData = {
  bankName: 'State Bank of India',
  AccountNumber: '**************',
  branchIFSCCode: 'SBIN0001234',
  branchName: 'Test Branch',
  bankAccountType: 'current',
  bankAccountHolderName: 'TEST CHESS CLUB'
};

let authToken = '';
let clubId = '';
let tournamentId = '';

// Helper function to make authenticated requests
const makeRequest = async (method, url, data = null) => {
  try {
    const config = {
      method,
      url: `${BASE_URL}${url}`,
      headers: authToken ? { Authorization: `Bearer ${authToken}` } : {},
      data
    };

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status || 500
    };
  }
};

// Test functions
const runTests = async () => {
  console.log('🚀 Starting Bank Verification and Payout Integration Tests\n');

  try {
    // 1. Register and Login
    console.log('1. 👤 Registering test club...');
    const registerResult = await makeRequest('POST', '/auth/register', {
      name: testClubData.name,
      email: testClubData.email,
      password: TEST_PASSWORD,
      role: 'club',
      phoneNumber: testClubData.phone
    });

    if (!registerResult.success && !registerResult.error.message?.includes('already exists')) {
      console.error('❌ Registration failed:', registerResult.error);
      return;
    }

    console.log('2. 🔐 Logging in...');
    const loginResult = await makeRequest('POST', '/auth/login', {
      email: testClubData.email,
      password: TEST_PASSWORD
    });

    if (!loginResult.success) {
      console.error('❌ Login failed:', loginResult.error);
      return;
    }

    authToken = loginResult.data.data.token;
    clubId = loginResult.data.data.user.id;
    console.log('✅ Login successful, Club ID:', clubId);

    // 3. Create Club Profile
    console.log('\n3. 🏢 Creating club profile...');
    const profileResult = await makeRequest('POST', '/club/profile', testClubData);
    if (profileResult.success) {
      console.log('✅ Club profile created successfully');
    } else {
      console.log('⚠️ Club profile creation failed (might already exist):', profileResult.error);
    }

    // 4. Create Banking Details (Now with fast response!)
    console.log('\n4. 🏦 Creating banking details with RazorPay setup...');
    console.log('⚡ This should now respond quickly (no more 75-second wait!)');

    const startTime = Date.now();
    const bankResult = await makeRequest('POST', '/club/bank/create', testBankData);
    const endTime = Date.now();
    const responseTime = (endTime - startTime) / 1000;

    console.log(`⏱️ Response time: ${responseTime.toFixed(2)} seconds`);

    if (bankResult.success) {
      console.log('✅ Banking details created successfully');
      console.log('📋 Setup Details:', JSON.stringify(bankResult.data.data.setup_details, null, 2));

      if (bankResult.data.data.verification_pending) {
        console.log('🔔 Verification is pending - webhook will complete it automatically');
        console.log('📝 Next Steps:', bankResult.data.data.next_steps);
      }

      if (bankResult.data.data.webhook_enabled) {
        console.log('🎯 Webhook-based verification is enabled - much better UX!');
      }
    } else {
      console.log('❌ Banking details creation failed:', bankResult.error);
      if (bankResult.error.error?.includes('already exist')) {
        console.log('ℹ️ Banking details already exist, continuing with tests...');
      } else {
        return;
      }
    }

    // 5. Get Banking Details
    console.log('\n5. 📄 Getting banking details...');
    const getBankResult = await makeRequest('GET', '/club/bank/details');
    
    if (getBankResult.success) {
      console.log('✅ Banking details retrieved successfully');
      console.log('🔒 Masked Account Number:', getBankResult.data.data.AccountNumber);
      console.log('✅ Verification Status:', getBankResult.data.data.verification_details.status);
    } else {
      console.log('❌ Failed to get banking details:', getBankResult.error);
    }

    // 6. Check Verification Status
    console.log('\n6. 🔍 Checking verification status...');
    const verifyStatusResult = await makeRequest('GET', '/club/bank/verify-status');
    
    if (verifyStatusResult.success) {
      console.log('✅ Verification status checked successfully');
      console.log('📊 Status:', verifyStatusResult.data.data.verification_status);
      console.log('💰 Amount Deposited:', verifyStatusResult.data.data.amount_deposited);
    } else {
      console.log('❌ Failed to check verification status:', verifyStatusResult.error);
    }

    // 7. Setup Payout
    console.log('\n7. ⚙️ Setting up payout infrastructure...');
    const payoutSetupResult = await makeRequest('POST', '/club/payout/setup');
    
    if (payoutSetupResult.success) {
      console.log('✅ Payout setup completed successfully');
      console.log('🆔 Contact ID:', payoutSetupResult.data.data.contact_id);
      console.log('🏦 Fund Account ID:', payoutSetupResult.data.data.fund_account_id);
    } else {
      console.log('❌ Payout setup failed:', payoutSetupResult.error);
      if (payoutSetupResult.error.error?.includes('already completed')) {
        console.log('ℹ️ Payout setup already completed, continuing...');
      }
    }

    // 8. Create Test Tournament (for payout calculation)
    console.log('\n8. 🏆 Creating test tournament...');
    const tournamentResult = await makeRequest('POST', '/tournament', {
      name: 'Test Tournament for Payout',
      entryFee: 500,
      participantCount: 20,
      totalCollected: 10000,
      status: 'completed',
      startDate: new Date().toISOString(),
      endDate: new Date(Date.now() + ********).toISOString()
    });

    if (tournamentResult.success) {
      tournamentId = tournamentResult.data.data.id;
      console.log('✅ Test tournament created, ID:', tournamentId);
    } else {
      console.log('❌ Failed to create tournament:', tournamentResult.error);
      // Use a dummy ID for calculation test
      tournamentId = '00000000-0000-0000-0000-000000000000';
    }

    // 9. Calculate Payout Preview
    console.log('\n9. 💰 Calculating payout preview...');
    const calcResult = await makeRequest('POST', `/club/payout/calculate/${tournamentId}`, {
      platformFeePercentage: 5,
      processingFeePercentage: 2.5,
      gstPercentage: 18
    });

    if (calcResult.success) {
      console.log('✅ Payout calculation successful');
      console.log('📊 Calculation Details:', JSON.stringify(calcResult.data.data.calculation, null, 2));
    } else {
      console.log('❌ Payout calculation failed:', calcResult.error);
    }

    // 10. Create Payout (if tournament exists)
    if (tournamentResult.success) {
      console.log('\n10. 💸 Creating tournament payout...');
      const createPayoutResult = await makeRequest('POST', '/club/payout/create', {
        tournament_id: tournamentId,
        urgent: false
      });

      if (createPayoutResult.success) {
        console.log('✅ Payout created successfully');
        console.log('🆔 Payout ID:', createPayoutResult.data.data.payout_id);
        console.log('💰 Amount:', createPayoutResult.data.data.amount);
        console.log('📊 Status:', createPayoutResult.data.data.status);

        // 11. Check Payout Status
        console.log('\n11. 📊 Checking payout status...');
        const payoutStatusResult = await makeRequest('GET', `/club/payout/status/${createPayoutResult.data.data.payout_id}`);
        
        if (payoutStatusResult.success) {
          console.log('✅ Payout status retrieved successfully');
          console.log('📊 Current Status:', payoutStatusResult.data.data.status);
          console.log('📝 Description:', payoutStatusResult.data.data.status_description);
        } else {
          console.log('❌ Failed to get payout status:', payoutStatusResult.error);
        }
      } else {
        console.log('❌ Payout creation failed:', createPayoutResult.error);
      }
    }

    // 12. Get All Payouts
    console.log('\n12. 📋 Getting all club payouts...');
    const allPayoutsResult = await makeRequest('GET', '/club/payout/list?page=1&limit=10');
    
    if (allPayoutsResult.success) {
      console.log('✅ Payouts list retrieved successfully');
      console.log('📊 Total Payouts:', allPayoutsResult.data.data.pagination.total);
      console.log('📋 Payouts:', JSON.stringify(allPayoutsResult.data.data.payouts, null, 2));
    } else {
      console.log('❌ Failed to get payouts list:', allPayoutsResult.error);
    }

    // 13. Toggle Payout Status
    console.log('\n13. 🔄 Testing payout toggle...');
    const toggleResult = await makeRequest('POST', '/club/bank/toggle-payout', { enable: true });
    
    if (toggleResult.success) {
      console.log('✅ Payout toggle successful');
      console.log('📊 Payout Enabled:', toggleResult.data.data.payout_enabled);
    } else {
      console.log('❌ Payout toggle failed (expected if not verified):', toggleResult.error);
    }

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📝 Summary:');
    console.log('- Bank details creation and verification flow tested');
    console.log('- RazorPay contact and fund account setup tested');
    console.log('- Payout calculation and creation tested');
    console.log('- Status checking and listing functionality tested');

  } catch (error) {
    console.error('💥 Unexpected error during tests:', error);
  }
};

// Run the tests
if (require.main === module) {
  runTests().then(() => {
    console.log('\n✅ Test execution completed');
    process.exit(0);
  }).catch((error) => {
    console.error('💥 Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = { runTests };
