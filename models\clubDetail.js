const { Model, DataTypes } = require("sequelize");

module.exports = (sequelize) => {
  class ClubDetail extends Model {
    static associate(models) {
      ClubDetail.belongsTo(models.User, {
        foreignKey: "userId",
        onDelete: "CASCADE",
        as: "user",
        scope: {
          role: "club",
        },
      });
      ClubDetail.hasMany(models.PlayerDetail, {
        foreignKey: "clubId",
        as: "players", 
      });
    }
  }

  ClubDetail.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      profileUrl: {
        type: DataTypes.STRING(2083),
        allowNull: true,
        validate: {
          isUrl: true,
        },
        field: 'profile_url',
      },
      clubDistrictId: {
        type: DataTypes.STRING(50),
        allowNull: true,
        field: 'club_district_id',
      },
      clubName: {
        type: DataTypes.STRING(),
        allowNull: false,
        field: 'club_name',
      },
      clubId: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true,
        field: 'club_id',
      },
      userId: {
        type: DataTypes.UUID,
        allowNull: false,
        unique: true,
        references: {
          model: "users",
          key: "id",
        },
        field: 'user_id',
      },
      contactPersonName: {
        type: DataTypes.STRING(100),
        allowNull: false,
        field: 'contact_person_name',
      },
      contactPersonNumber: {
        type: DataTypes.STRING(15),
        allowNull: false,
        field: 'contact_person_number',
      },
      contactPersonEmail: {
        type: DataTypes.STRING(100),
        allowNull: false,
        validate: {
          isEmail: true,
        },
        field: 'contact_person_email',
      },
      alternateContactNumber: {
        type: DataTypes.STRING(15),
        allowNull: true,
        field: 'alternate_contact_number',
      },
      country: {
        type: DataTypes.STRING(50),
        allowNull: false,
      },
      state: {
        type: DataTypes.STRING(50),
        allowNull: false,
      },
      district: {
        type: DataTypes.STRING(50),
        allowNull: false,
      },
      city: {
        type: DataTypes.STRING(50),
        allowNull: false,
      },
      pincode: {
        type: DataTypes.STRING(10),
        allowNull: false,
      },
      address: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      locationUrl: {
        type: DataTypes.STRING(2083),
        allowNull: true,
        validate: {
          isUrl: true,
        },
        field: 'location_url',
      },
      authorizedSignatoryName: {
        type: DataTypes.STRING(100),
        allowNull: false,
        field: 'authorized_signatory_name',
      },
      authorizedSignatoryContactNumber: {
        type: DataTypes.STRING(15),
        allowNull: false,
        field: 'authorized_signatory_contact_number',
      },
      authorizedSignatoryEmail: {
        type: DataTypes.STRING(100),
        allowNull: false,
        validate: {
          isEmail: true,
        },
        field: 'authorized_signatory_email',
      },
      authorizedSignatoryDesignation: {
        type: DataTypes.STRING(100),
        allowNull: false,
        field: 'authorized_signatory_designation',
      },
    },
    {
      sequelize,
      modelName: "ClubDetail",
      tableName: "club_details",
      timestamps: true,
    }
  );

  return ClubDetail;
};
