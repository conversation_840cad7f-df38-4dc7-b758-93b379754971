'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn('bank_details', 'payout_enabled', {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
      allowNull: false,
      field: 'payout_enabled'
    });

  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeColumn('bank_details', 'payout_enabled');
  }
};
